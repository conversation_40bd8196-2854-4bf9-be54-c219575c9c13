{"version": 3, "sources": ["../../src/server/serve-static.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport send from 'next/dist/compiled/send'\n\n// TODO: Remove this once \"send\" has updated the \"mime\", or next.js use custom version of \"mime\"\n// Although \"mime\" has already add avif in version 2.4.7, \"send\" is still using mime@1.6.0\nsend.mime.define({\n  'image/avif': ['avif'],\n  'image/x-icns': ['icns'],\n})\n\nexport function serveStatic(\n  req: IncomingMessage,\n  res: ServerResponse,\n  path: string,\n  opts?: Parameters<typeof send>[2]\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    send(req, path, opts)\n      .on('directory', () => {\n        // We don't allow directories to be read.\n        const err: any = new Error('No directory access')\n        err.code = 'ENOENT'\n        reject(err)\n      })\n      .on('error', reject)\n      .pipe(res)\n      .on('finish', resolve)\n  })\n}\n\nexport const getContentType: (extWithoutDot: string) => string | null =\n  'getType' in send.mime\n    ? (extWithoutDot: string) => send.mime.getType(extWithoutDot)\n    : (extWithoutDot: string) => (send.mime as any).lookup(extWithoutDot)\n\nexport const getExtension: (contentType: string) => string | null =\n  'getExtension' in send.mime\n    ? (contentType: string) => send.mime.getExtension(contentType)\n    : (contentType: string) => (send.mime as any).extension(contentType)\n"], "names": ["send", "mime", "define", "serveStatic", "req", "res", "path", "opts", "Promise", "resolve", "reject", "on", "err", "Error", "code", "pipe", "getContentType", "extWithoutDot", "getType", "lookup", "getExtension", "contentType", "extension"], "mappings": "AACA,OAAOA,UAAU,0BAAyB;AAE1C,gGAAgG;AAChG,0FAA0F;AAC1FA,KAAKC,IAAI,CAACC,MAAM,CAAC;IACf,cAAc;QAAC;KAAO;IACtB,gBAAgB;QAAC;KAAO;AAC1B;AAEA,OAAO,SAASC,YACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAY,EACZC,IAAiC;IAEjC,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BV,KAAKI,KAAKE,MAAMC,MACbI,EAAE,CAAC,aAAa;YACf,yCAAyC;YACzC,MAAMC,MAAW,qBAAgC,CAAhC,IAAIC,MAAM,wBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+B;YAChDD,IAAIE,IAAI,GAAG;YACXJ,OAAOE;QACT,GACCD,EAAE,CAAC,SAASD,QACZK,IAAI,CAACV,KACLM,EAAE,CAAC,UAAUF;IAClB;AACF;AAEA,OAAO,MAAMO,iBACX,aAAahB,KAAKC,IAAI,GAClB,CAACgB,gBAA0BjB,KAAKC,IAAI,CAACiB,OAAO,CAACD,iBAC7C,CAACA,gBAA0B,AAACjB,KAAKC,IAAI,CAASkB,MAAM,CAACF,eAAc;AAEzE,OAAO,MAAMG,eACX,kBAAkBpB,KAAKC,IAAI,GACvB,CAACoB,cAAwBrB,KAAKC,IAAI,CAACmB,YAAY,CAACC,eAChD,CAACA,cAAwB,AAACrB,KAAKC,IAAI,CAASqB,SAAS,CAACD,aAAY"}