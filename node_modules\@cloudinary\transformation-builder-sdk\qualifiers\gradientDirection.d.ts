import { GradientDirectionQualifierValue } from "./gradientDirection/GradientDirectionQualifierValue.js";
/**
 * @description Defines the direction for a background gradient fade effect.
 * @memberOf Qualifiers
 * @namespace GradientDirection
 */
/**
 * @summary qualifier
 * @description Blend the colors horizontally.
 * @memberOf Qualifiers.GradientDirection
 * @return {Qualifiers.GradientDirection.GradientDirectionQualifierValue}
 */
declare function horizontal(): GradientDirectionQualifierValue;
/**
 * @summary qualifier
 * @description Blend the colors vertically.
 * @memberOf Qualifiers.GradientDirection
 * @return {Qualifiers.GradientDirection.GradientDirectionQualifierValue}
 */
declare function vertical(): GradientDirectionQualifierValue;
/**
 * @summary qualifier
 * @description Blend the colors diagonally from top-left to bottom-right.
 * @memberOf Qualifiers.GradientDirection
 * @return {Qualifiers.GradientDirection.GradientDirectionQualifierValue}
 */
declare function diagonalDesc(): GradientDirectionQualifierValue;
/**
 * @summary qualifier
 * @description Blend the colors diagonally from bottom-left to top-right.
 * @memberOf Qualifiers.GradientDirection
 * @return {Qualifiers.GradientDirection.GradientDirectionQualifierValue}
 */
declare function diagonalAsc(): GradientDirectionQualifierValue;
declare const GradientDirection: {
    horizontal: typeof horizontal;
    vertical: typeof vertical;
    diagonalDesc: typeof diagonalDesc;
    diagonalAsc: typeof diagonalAsc;
};
export { horizontal, vertical, diagonalDesc, diagonalAsc, GradientDirection };
