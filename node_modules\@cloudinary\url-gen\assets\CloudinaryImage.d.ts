import { CloudinaryTransformable } from "./CloudinaryTransformable.js";
import ICloudConfig from "../config/interfaces/Config/ICloudConfig.js";
import IURLConfig from "../config/interfaces/Config/IURLConfig.js";
/**
 * @desc Cloudinary image asset, with image-related transformations
 * @summary SDK
 * @memberOf SDK
 */
declare class CloudinaryImage extends CloudinaryTransformable {
    constructor(publicID?: string, cloudConfig?: ICloudConfig, urlConfig?: IURLConfig);
}
export { CloudinaryImage };
