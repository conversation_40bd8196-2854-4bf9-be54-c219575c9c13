{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/app/products/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { usePermissions } from '@/components/providers/AuthProvider'\nimport { Button } from '@/components/ui/Button'\nimport { DataTable } from '@/components/ui/DataTable'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { productService, categoryService } from '@/lib/services/productService'\nimport { formatCurrency } from '@/utils'\nimport type { Product, Category, ProductFilters, PaginationParams } from '@/types'\n\nfunction ProductsContent() {\n  const { canPerformAction } = usePermissions()\n  const [products, setProducts] = useState<Product[]>([])\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    totalPages: 0\n  })\n  const [filters, setFilters] = useState<ProductFilters>({})\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    loadProducts()\n    loadCategories()\n  }, [pagination.page, pagination.limit, filters])\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true)\n      const params: PaginationParams & ProductFilters = {\n        page: pagination.page,\n        limit: pagination.limit,\n        ...filters\n      }\n      \n      const response = await productService.getProducts(params)\n      setProducts(response.data)\n      setPagination(response.pagination)\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Failed to load products')\n      console.error('Failed to load products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadCategories = async () => {\n    try {\n      const data = await categoryService.getCategories()\n      setCategories(data)\n    } catch (error) {\n      console.error('Failed to load categories:', error)\n    }\n  }\n\n  const handleSearch = (search: string) => {\n    setFilters(prev => ({ ...prev, search }))\n    setPagination(prev => ({ ...prev, page: 1 }))\n  }\n\n  const handleSort = (column: string, direction: 'asc' | 'desc') => {\n    setFilters(prev => ({ ...prev, sort_by: column, sort_order: direction }))\n  }\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }))\n  }\n\n  const handleDeleteProduct = async (product: Product) => {\n    if (!canPerformAction('delete_product')) {\n      alert('You do not have permission to delete products')\n      return\n    }\n\n    if (!confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n      return\n    }\n\n    try {\n      await productService.deleteProduct(product.id)\n      loadProducts() // Reload the list\n    } catch (error) {\n      alert(error instanceof Error ? error.message : 'Failed to delete product')\n    }\n  }\n\n  const columns = [\n    {\n      key: 'image_url',\n      header: 'Image',\n      render: (product: Product) => (\n        <div className=\"w-12 h-12 relative\">\n          {product.image_url ? (\n            <Image\n              src={product.image_url}\n              alt={product.name}\n              fill\n              className=\"object-cover rounded-md\"\n            />\n          ) : (\n            <div className=\"w-full h-full bg-gray-200 rounded-md flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n            </div>\n          )}\n        </div>\n      )\n    },\n    {\n      key: 'name',\n      header: 'Product Name',\n      sortable: true,\n      render: (product: Product) => (\n        <div>\n          <div className=\"font-medium text-gray-900\">{product.name}</div>\n          {product.net_weight && (\n            <div className=\"text-sm text-gray-500\">{product.net_weight}</div>\n          )}\n        </div>\n      )\n    },\n    {\n      key: 'category',\n      header: 'Category',\n      render: (product: Product) => (\n        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n          {product.category?.name || 'Uncategorized'}\n        </span>\n      )\n    },\n    {\n      key: 'price',\n      header: 'Price',\n      sortable: true,\n      render: (product: Product) => (\n        <span className=\"font-medium\">{formatCurrency(product.price, 'PHP')}</span>\n      )\n    },\n    {\n      key: 'stock_quantity',\n      header: 'Stock',\n      sortable: true,\n      render: (product: Product) => (\n        <span className={`font-medium ${\n          product.stock_quantity <= 10 \n            ? 'text-red-600' \n            : product.stock_quantity <= 20 \n            ? 'text-yellow-600' \n            : 'text-green-600'\n        }`}>\n          {product.stock_quantity}\n        </span>\n      )\n    },\n    {\n      key: 'is_active',\n      header: 'Status',\n      render: (product: Product) => (\n        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n          product.is_active \n            ? 'bg-green-100 text-green-800' \n            : 'bg-red-100 text-red-800'\n        }`}>\n          {product.is_active ? 'Active' : 'Inactive'}\n        </span>\n      )\n    }\n  ]\n\n  const renderActions = (product: Product) => (\n    <div className=\"flex items-center space-x-2\">\n      <Link href={`/products/${product.id}`}>\n        <Button variant=\"outline\" size=\"sm\">\n          View\n        </Button>\n      </Link>\n      {canPerformAction('edit_product') && (\n        <Link href={`/products/${product.id}/edit`}>\n          <Button variant=\"outline\" size=\"sm\">\n            Edit\n          </Button>\n        </Link>\n      )}\n      {canPerformAction('delete_product') && (\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handleDeleteProduct(product)}\n          className=\"text-red-600 hover:text-red-700\"\n        >\n          Delete\n        </Button>\n      )}\n    </div>\n  )\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Products</h1>\n          <p className=\"text-gray-600 mt-2\">Manage your store inventory</p>\n        </div>\n        {canPerformAction('create_product') && (\n          <Link href=\"/products/new\">\n            <Button>Add New Product</Button>\n          </Link>\n        )}\n      </div>\n\n      {/* Filters */}\n      <Card className=\"mb-6\">\n        <CardHeader>\n          <CardTitle>Filters</CardTitle>\n          <CardDescription>Filter products by category and status</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Category\n              </label>\n              <select\n                value={filters.category_id || ''}\n                onChange={(e) => setFilters(prev => ({ ...prev, category_id: e.target.value || undefined }))}\n                className=\"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Status\n              </label>\n              <select\n                value={filters.is_active?.toString() || ''}\n                onChange={(e) => setFilters(prev => ({ \n                  ...prev, \n                  is_active: e.target.value ? e.target.value === 'true' : undefined \n                }))}\n                className=\"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">All Status</option>\n                <option value=\"true\">Active</option>\n                <option value=\"false\">Inactive</option>\n              </select>\n            </div>\n\n            <div className=\"flex items-end\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={filters.low_stock || false}\n                  onChange={(e) => setFilters(prev => ({ ...prev, low_stock: e.target.checked || undefined }))}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700\">Low Stock Only</span>\n              </label>\n            </div>\n\n            <div className=\"flex items-end\">\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setFilters({})\n                  setPagination(prev => ({ ...prev, page: 1 }))\n                }}\n              >\n                Clear Filters\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-6\">\n          <p className=\"text-sm text-red-600\">{error}</p>\n        </div>\n      )}\n\n      <DataTable\n        data={products}\n        columns={columns}\n        loading={loading}\n        pagination={pagination}\n        onPageChange={handlePageChange}\n        onSearch={handleSearch}\n        onSort={handleSort}\n        searchPlaceholder=\"Search products...\"\n        emptyMessage=\"No products found\"\n        actions={renderActions}\n      />\n    </div>\n  )\n}\n\nexport default function ProductsPage() {\n  return (\n    <ProtectedRoute requiredPermission=\"view_analytics\">\n      <ProductsContent />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJA;;;;;;;;;;;;AAcA,SAAS;IACP,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACxD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC,WAAW,IAAI;QAAE,WAAW,KAAK;QAAE;KAAQ;IAE/C,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,SAA4C;gBAChD,MAAM,WAAW,IAAI;gBACrB,OAAO,WAAW,KAAK;gBACvB,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,eAAe,WAAW,CAAC;YAClD,YAAY,SAAS,IAAI;YACzB,cAAc,SAAS,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,gBAAgB,aAAa;YAChD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAO,CAAC;QACvC,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,MAAM,aAAa,CAAC,QAAgB;QAClC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAQ,YAAY;YAAU,CAAC;IACzE;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;IAC1C;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,iBAAiB,mBAAmB;YACvC,MAAM;YACN;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,iCAAiC,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG;YAClE;QACF;QAEA,IAAI;YACF,MAAM,eAAe,aAAa,CAAC,QAAQ,EAAE;YAC7C,eAAe,kBAAkB;;QACnC,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjD;IACF;IAEA,MAAM,UAAU;QACd;YACE,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,wBACP,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,SAAS,iBAChB,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,SAAS;wBACtB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC5E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;QAMjF;QACA;YACE,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,wBACP,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCAA6B,QAAQ,IAAI;;;;;;wBACvD,QAAQ,UAAU,kBACjB,8OAAC;4BAAI,WAAU;sCAAyB,QAAQ,UAAU;;;;;;;;;;;;QAIlE;QACA;YACE,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,wBACP,8OAAC;oBAAK,WAAU;8BACb,QAAQ,QAAQ,EAAE,QAAQ;;;;;;QAGjC;QACA;YACE,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,wBACP,8OAAC;oBAAK,WAAU;8BAAe,eAAe,QAAQ,KAAK,EAAE;;;;;;QAEjE;QACA;YACE,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,wBACP,8OAAC;oBAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,cAAc,IAAI,KACtB,iBACA,QAAQ,cAAc,IAAI,KAC1B,oBACA,kBACJ;8BACC,QAAQ,cAAc;;;;;;QAG7B;QACA;YACE,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,wBACP,8OAAC;oBAAK,WAAW,CAAC,wEAAwE,EACxF,QAAQ,SAAS,GACb,gCACA,2BACJ;8BACC,QAAQ,SAAS,GAAG,WAAW;;;;;;QAGtC;KACD;IAED,MAAM,gBAAgB,CAAC,wBACrB,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;8BACnC,cAAA,8OAAC;wBAAO,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;gBAIrC,iBAAiB,iCAChB,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;8BACxC,cAAA,8OAAC;wBAAO,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;gBAKvC,iBAAiB,mCAChB,8OAAC;oBACC,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,oBAAoB;oBACnC,WAAU;8BACX;;;;;;;;;;;;IAOP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAEnC,iBAAiB,mCAChB,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC;sCAAO;;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;;0CACC,8OAAC;0CAAU;;;;;;0CACX,8OAAC;0CAAgB;;;;;;;;;;;;kCAEnB,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,QAAQ,WAAW,IAAI;4CAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK,IAAI;oDAAU,CAAC;4CAC1F,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAO9B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,QAAQ,SAAS,EAAE,cAAc;4CACxC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDACnC,GAAG,IAAI;wDACP,WAAW,EAAE,MAAM,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK,KAAK,SAAS;oDAC1D,CAAC;4CACD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,QAAQ,SAAS,IAAI;gDAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,EAAE,MAAM,CAAC,OAAO,IAAI;wDAAU,CAAC;gDAC1F,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAI5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAQ;wCACR,SAAS;4CACP,WAAW,CAAC;4CACZ,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM;gDAAE,CAAC;wCAC7C;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQR,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAIzC,8OAAC;gBACC,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,mBAAkB;gBAClB,cAAa;gBACb,SAAS;;;;;;;;;;;;AAIjB;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAe,oBAAmB;kBACjC,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}