/**
 * @memberOf Qualifiers
 * @namespace Quality
 * @see Visit {@link Actions.Delivery.quality|Delivery Quality} for an example
 */
/**
 * @summary qualifier
 * @description Quality auto
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function auto(): string;
/**
 * @summary qualifier
 * @description Quality best
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function autoBest(): string;
/**
 * @summary qualifier
 * @description Quality eco
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function autoEco(): string;
/**
 * @summary qualifier
 * @description Quality good
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function autoGood(): string;
/**
 * @summary qualifier
 * @description Quality low
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function autoLow(): string;
/**
 * @summary qualifier
 * @description Quality jpegmini
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function jpegmini(): string;
/**
 * @summary qualifier
 * @description Quality jpegmini best
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function jpegminiBest(): string;
/**
 * @summary qualifier
 * @description Quality jpegmini high
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function jpegminiHigh(): string;
/**
 * @summary qualifier
 * @quality
 * @description Quality jpegmini medium
 * @memberOf Qualifiers.Quality
 * @return {string}
 */
declare function jpegminiMedium(): string;
declare const Quality: {
    auto: typeof auto;
    autoBest: typeof autoBest;
    autoEco: typeof autoEco;
    autoGood: typeof autoGood;
    autoLow: typeof autoLow;
    jpegmini: typeof jpegmini;
    jpegminiBest: typeof jpegminiBest;
    jpegminiHigh: typeof jpegminiHigh;
    jpegminiMedium: typeof jpegminiMedium;
};
export { Quality, auto, autoBest, autoEco, autoGood, autoLow, jpegmini, jpegminiBest, jpegminiHigh, jpegminiMedium };
