(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2316:(e,t,s)=>{Promise.resolve().then(s.bind(s,3792))},3792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var i=s(5155),a=s(9176),r=s(7141),l=s(8591);function n(){let{isAuthenticated:e}=(0,l.GV)(e=>e.auth);return(0,i.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen",children:[(0,i.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,i.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Welcome to ",(0,i.jsx)("span",{className:"text-blue-600",children:r.C3})]}),(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"A professional e-commerce platform built with Next.js 15, TypeScript, Tailwind CSS, Redux Toolkit, and Supabase. Experience modern web development at its finest."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(a.$,{size:"lg",children:"Get Started"}),(0,i.jsx)(a.$,{variant:"outline",size:"lg",children:"Learn More"})]})]})}),(0,i.jsx)("section",{className:"py-16 px-4 sm:px-6 lg:px-8 bg-white",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Tech Stack Features"}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,i.jsx)(o,{title:"Next.js 15",description:"Latest version with App Router, Server Components, and Turbopack for blazing fast development.",icon:"⚡"}),(0,i.jsx)(o,{title:"TypeScript",description:"Full type safety across the entire application for better developer experience and fewer bugs.",icon:"\uD83D\uDD12"}),(0,i.jsx)(o,{title:"Tailwind CSS",description:"Utility-first CSS framework for rapid UI development with consistent design.",icon:"\uD83C\uDFA8"}),(0,i.jsx)(o,{title:"Redux Toolkit",description:"Predictable state management with modern Redux patterns and excellent DevTools.",icon:"\uD83D\uDD04"}),(0,i.jsx)(o,{title:"Supabase",description:"Open source Firebase alternative with PostgreSQL database and real-time features.",icon:"\uD83D\uDDC4️"}),(0,i.jsx)(o,{title:"Professional Setup",description:"ESLint, Prettier, and VS Code configuration for optimal development workflow.",icon:"⚙️"})]})]})}),(0,i.jsx)("section",{className:"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Installation Status"}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsx)(d,{label:"Next.js 15 + TypeScript",status:"✅"}),(0,i.jsx)(d,{label:"Tailwind CSS v4",status:"✅"}),(0,i.jsx)(d,{label:"Redux Toolkit",status:"✅"}),(0,i.jsx)(d,{label:"Supabase Integration",status:"✅"}),(0,i.jsx)(d,{label:"ESLint + Prettier",status:"✅"}),(0,i.jsx)(d,{label:"Professional Structure",status:"✅"})]}),(0,i.jsxs)("p",{className:"mt-6 text-gray-600",children:["Authentication Status: ",e?"\uD83D\uDFE2 Logged In":"\uD83D\uDD34 Not Logged In"]})]})]})})]})}function o(e){let{title:t,description:s,icon:a}=e;return(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[(0,i.jsx)("div",{className:"text-3xl mb-4",children:a}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:t}),(0,i.jsx)("p",{className:"text-gray-600",children:s})]})}function d(e){let{label:t,status:s}=e;return(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("span",{className:"font-medium text-gray-900",children:t}),(0,i.jsx)("span",{className:"text-lg",children:s})]})}},7141:(e,t,s)=>{"use strict";s.d(t,{C3:()=>i,bw:()=>a});let i="Caparan Tindahan",a={HOME:"/",LOGIN:"/login",REGISTER:"/register",DASHBOARD:"/dashboard",PROFILE:"/profile",PRODUCTS:"/products"}},8591:(e,t,s)=>{"use strict";s.d(t,{M_:()=>c,GV:()=>x});var i=s(1990),a=s(4540);let r=(0,i.Z0)({name:"auth",initialState:{user:null,isLoading:!1,isAuthenticated:!1},reducers:{setUser:(e,t)=>{e.user=t.payload,e.isAuthenticated=!0,e.isLoading=!1},clearUser:e=>{e.user=null,e.isAuthenticated=!1,e.isLoading=!1},setLoading:(e,t)=>{e.isLoading=t.payload}}}),{setUser:l,clearUser:n,setLoading:o}=r.actions,d=r.reducer,c=(0,i.U1)({reducer:{auth:d},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"]}})}),x=a.d4},9176:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var i=s(5155),a=s(2115),r=s(2596);let l=(0,a.forwardRef)((e,t)=>{let{className:s,variant:a="primary",size:l="md",isLoading:n,children:o,disabled:d,...c}=e;return(0,i.jsxs)("button",{className:function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.$)(t)}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-600 text-white hover:bg-gray-700",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100"}[a],{sm:"h-8 px-3 text-sm",md:"h-10 px-4",lg:"h-12 px-6 text-lg"}[l],s),ref:t,disabled:d||n,...c,children:[n&&(0,i.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",viewBox:"0 0 24 24",children:[(0,i.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,i.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});l.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[254,441,684,358],()=>t(2316)),_N_E=e.O()}]);