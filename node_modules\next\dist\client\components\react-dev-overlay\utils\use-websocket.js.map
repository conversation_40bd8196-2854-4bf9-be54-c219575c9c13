{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/use-websocket.ts"], "sourcesContent": ["import { useCallback, useContext, useEffect, useRef } from 'react'\nimport { GlobalLayoutRouterContext } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { getSocketUrl } from './get-socket-url'\nimport type { TurbopackMsgToBrowser } from '../../../../server/dev/hot-reloader-types'\n\nexport function useWebsocket(assetPrefix: string) {\n  const webSocketRef = useRef<WebSocket>(undefined)\n\n  useEffect(() => {\n    if (webSocketRef.current) {\n      return\n    }\n\n    const url = getSocketUrl(assetPrefix)\n\n    webSocketRef.current = new window.WebSocket(`${url}/_next/webpack-hmr`)\n  }, [assetPrefix])\n\n  return webSocketRef\n}\n\nexport function useSendMessage(webSocketRef: ReturnType<typeof useWebsocket>) {\n  const sendMessage = useCallback(\n    (data: string) => {\n      const socket = webSocketRef.current\n      if (!socket || socket.readyState !== socket.OPEN) {\n        return\n      }\n      return socket.send(data)\n    },\n    [webSocketRef]\n  )\n  return sendMessage\n}\n\nexport function useTurbopack(\n  sendMessage: ReturnType<typeof useSendMessage>,\n  onUpdateError: (err: unknown) => void\n) {\n  const turbopackState = useRef<{\n    init: boolean\n    queue: Array<TurbopackMsgToBrowser> | undefined\n    callback: ((msg: TurbopackMsgToBrowser) => void) | undefined\n  }>({\n    init: false,\n    // Until the dynamic import resolves, queue any turbopack messages which will be replayed.\n    queue: [],\n    callback: undefined,\n  })\n\n  const processTurbopackMessage = useCallback((msg: TurbopackMsgToBrowser) => {\n    const { callback, queue } = turbopackState.current\n    if (callback) {\n      callback(msg)\n    } else {\n      queue!.push(msg)\n    }\n  }, [])\n\n  useEffect(() => {\n    const { current: initCurrent } = turbopackState\n    // TODO(WEB-1589): only install if `process.turbopack` set.\n    if (initCurrent.init) {\n      return\n    }\n    initCurrent.init = true\n\n    import(\n      // @ts-expect-error requires \"moduleResolution\": \"node16\" in tsconfig.json and not .ts extension\n      '@vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts'\n    ).then(({ connect }) => {\n      const { current } = turbopackState\n      connect({\n        addMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n          current.callback = cb\n\n          // Replay all Turbopack messages before we were able to establish the HMR client.\n          for (const msg of current.queue!) {\n            cb(msg)\n          }\n          current.queue = undefined\n        },\n        sendMessage,\n        onUpdateError,\n      })\n    })\n  }, [sendMessage, onUpdateError])\n\n  return processTurbopackMessage\n}\n\nexport function useWebsocketPing(\n  websocketRef: ReturnType<typeof useWebsocket>\n) {\n  const sendMessage = useSendMessage(websocketRef)\n  const { tree } = useContext(GlobalLayoutRouterContext)\n\n  useEffect(() => {\n    // Never send pings when using Turbopack as it's not used.\n    // Pings were originally used to keep track of active routes in on-demand-entries with webpack.\n    if (process.env.TURBOPACK) {\n      return\n    }\n\n    // Taken from on-demand-entries-client.js\n    const interval = setInterval(() => {\n      sendMessage(\n        JSON.stringify({\n          event: 'ping',\n          tree,\n          appDirRoute: true,\n        })\n      )\n    }, 2500)\n    return () => clearInterval(interval)\n  }, [tree, sendMessage])\n}\n"], "names": ["useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "assetPrefix", "webSocketRef", "useRef", "undefined", "useEffect", "current", "url", "getSocketUrl", "window", "WebSocket", "sendMessage", "useCallback", "data", "socket", "readyState", "OPEN", "send", "onUpdateError", "turbopackState", "init", "queue", "callback", "processTurbopackMessage", "msg", "push", "initCurrent", "then", "connect", "addMessageListener", "cb", "websocketRef", "tree", "useContext", "GlobalLayoutRouterContext", "process", "env", "TURBOPACK", "interval", "setInterval", "JSON", "stringify", "event", "appDirRoute", "clearInterval"], "mappings": ";;;;;;;;;;;;;;;;;IAqBgBA,cAAc;eAAdA;;IAcAC,YAAY;eAAZA;;IA9BAC,YAAY;eAAZA;;IAsFAC,gBAAgB;eAAhBA;;;uBA3F2C;+CACjB;8BACb;AAGtB,SAASD,aAAaE,WAAmB;IAC9C,MAAMC,eAAeC,IAAAA,aAAM,EAAYC;IAEvCC,IAAAA,gBAAS,EAAC;QACR,IAAIH,aAAaI,OAAO,EAAE;YACxB;QACF;QAEA,MAAMC,MAAMC,IAAAA,0BAAY,EAACP;QAEzBC,aAAaI,OAAO,GAAG,IAAIG,OAAOC,SAAS,CAAC,AAAC,KAAEH,MAAI;IACrD,GAAG;QAACN;KAAY;IAEhB,OAAOC;AACT;AAEO,SAASL,eAAeK,YAA6C;IAC1E,MAAMS,cAAcC,IAAAA,kBAAW,EAC7B,CAACC;QACC,MAAMC,SAASZ,aAAaI,OAAO;QACnC,IAAI,CAACQ,UAAUA,OAAOC,UAAU,KAAKD,OAAOE,IAAI,EAAE;YAChD;QACF;QACA,OAAOF,OAAOG,IAAI,CAACJ;IACrB,GACA;QAACX;KAAa;IAEhB,OAAOS;AACT;AAEO,SAASb,aACda,WAA8C,EAC9CO,aAAqC;IAErC,MAAMC,iBAAiBhB,IAAAA,aAAM,EAI1B;QACDiB,MAAM;QACN,0FAA0F;QAC1FC,OAAO,EAAE;QACTC,UAAUlB;IACZ;IAEA,MAAMmB,0BAA0BX,IAAAA,kBAAW,EAAC,CAACY;QAC3C,MAAM,EAAEF,QAAQ,EAAED,KAAK,EAAE,GAAGF,eAAeb,OAAO;QAClD,IAAIgB,UAAU;YACZA,SAASE;QACX,OAAO;YACLH,MAAOI,IAAI,CAACD;QACd;IACF,GAAG,EAAE;IAELnB,IAAAA,gBAAS,EAAC;QACR,MAAM,EAAEC,SAASoB,WAAW,EAAE,GAAGP;QACjC,2DAA2D;QAC3D,IAAIO,YAAYN,IAAI,EAAE;YACpB;QACF;QACAM,YAAYN,IAAI,GAAG;QAEnB,MAAM,CACJ,gGAAgG;QAChG,6EACAO,IAAI,CAAC;gBAAC,EAAEC,OAAO,EAAE;YACjB,MAAM,EAAEtB,OAAO,EAAE,GAAGa;YACpBS,QAAQ;gBACNC,oBAAmBC,EAAwC;oBACzDxB,QAAQgB,QAAQ,GAAGQ;oBAEnB,iFAAiF;oBACjF,KAAK,MAAMN,OAAOlB,QAAQe,KAAK,CAAG;wBAChCS,GAAGN;oBACL;oBACAlB,QAAQe,KAAK,GAAGjB;gBAClB;gBACAO;gBACAO;YACF;QACF;IACF,GAAG;QAACP;QAAaO;KAAc;IAE/B,OAAOK;AACT;AAEO,SAASvB,iBACd+B,YAA6C;IAE7C,MAAMpB,cAAcd,eAAekC;IACnC,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,iBAAU,EAACC,wDAAyB;IAErD7B,IAAAA,gBAAS,EAAC;QACR,0DAA0D;QAC1D,+FAA+F;QAC/F,IAAI8B,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB;QACF;QAEA,yCAAyC;QACzC,MAAMC,WAAWC,YAAY;YAC3B5B,YACE6B,KAAKC,SAAS,CAAC;gBACbC,OAAO;gBACPV;gBACAW,aAAa;YACf;QAEJ,GAAG;QACH,OAAO,IAAMC,cAAcN;IAC7B,GAAG;QAACN;QAAMrB;KAAY;AACxB"}