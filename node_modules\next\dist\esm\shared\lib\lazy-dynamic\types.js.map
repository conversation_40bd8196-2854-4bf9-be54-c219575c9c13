{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/types.ts"], "sourcesContent": ["export type ComponentModule<P = {}> = { default: React.ComponentType<P> }\n\nexport declare type LoaderComponent<P = {}> = Promise<\n  React.ComponentType<P> | ComponentModule<P>\n>\n\nexport declare type Loader<P = {}> = () => LoaderComponent<P>\n\nexport type LoadableGeneratedOptions = {\n  modules?: string[]\n}\n\nexport type DynamicOptionsLoadingProps = {\n  error?: Error | null\n  isLoading?: boolean\n  pastDelay?: boolean\n  retry?: () => void\n  timedOut?: boolean\n}\n"], "names": [], "mappings": "AAYA,WAMC"}