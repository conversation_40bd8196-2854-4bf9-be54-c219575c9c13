{"version": 3, "sources": ["../../src/lib/verifyAndLint.ts"], "sourcesContent": ["import { red } from './picocolors'\nimport { Worker } from './worker'\nimport { existsSync } from 'fs'\nimport { join } from 'path'\nimport { ESLINT_DEFAULT_DIRS } from './constants'\nimport type { Telemetry } from '../telemetry/storage'\nimport { eventLintCheckCompleted } from '../telemetry/events'\nimport { CompileError } from './compile-error'\nimport isError from './is-error'\n\nexport async function verifyAndLint(\n  dir: string,\n  cacheLocation: string,\n  configLintDirs: string[] | undefined,\n  enableWorkerThreads: boolean | undefined,\n  telemetry: Telemetry\n): Promise<void> {\n  let lintWorkers:\n    | (Worker & {\n        runLintCheck: typeof import('./eslint/runLintCheck').runLintCheck\n      })\n    | undefined\n\n  try {\n    lintWorkers = new Worker(require.resolve('./eslint/runLintCheck'), {\n      exposedMethods: ['runLintCheck'],\n      numWorkers: 1,\n      enableWorkerThreads,\n      maxRetries: 0,\n    }) as Worker & {\n      runLintCheck: typeof import('./eslint/runLintCheck').runLintCheck\n    }\n\n    const lintDirs = (configLintDirs ?? ESLINT_DEFAULT_DIRS).reduce(\n      (res: string[], d: string) => {\n        const currDir = join(dir, d)\n        if (!existsSync(currDir)) return res\n        res.push(currDir)\n        return res\n      },\n      []\n    )\n\n    const lintResults = await lintWorkers?.runLintCheck(dir, lintDirs, {\n      lintDuringBuild: true,\n      eslintOptions: {\n        cacheLocation,\n      },\n    })\n    const lintOutput =\n      typeof lintResults === 'string' ? lintResults : lintResults?.output\n\n    if (typeof lintResults !== 'string' && lintResults?.eventInfo) {\n      telemetry.record(\n        eventLintCheckCompleted({\n          ...lintResults.eventInfo,\n          buildLint: true,\n        })\n      )\n    }\n\n    if (typeof lintResults !== 'string' && lintResults?.isError && lintOutput) {\n      await telemetry.flush()\n      throw new CompileError(lintOutput)\n    }\n\n    if (lintOutput) {\n      console.log(lintOutput)\n    }\n  } catch (err) {\n    if (isError(err)) {\n      if (err.type === 'CompileError' || err instanceof CompileError) {\n        console.error(red('\\nFailed to compile.'))\n        console.error(err.message)\n        process.exit(1)\n      } else if (err.type === 'FatalError') {\n        console.error(err.message)\n        process.exit(1)\n      }\n    }\n    throw err\n  } finally {\n    try {\n      lintWorkers?.end()\n    } catch {}\n  }\n}\n"], "names": ["red", "Worker", "existsSync", "join", "ESLINT_DEFAULT_DIRS", "eventLintCheckCompleted", "CompileError", "isError", "verifyAndLint", "dir", "cacheLocation", "configLintDirs", "enableWorkerThreads", "telemetry", "lintWorkers", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "lintDirs", "reduce", "res", "d", "currDir", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "buildLint", "flush", "console", "log", "err", "type", "error", "message", "process", "exit", "end"], "mappings": "AAAA,SAASA,GAAG,QAAQ,eAAc;AAClC,SAASC,MAAM,QAAQ,WAAU;AACjC,SAASC,UAAU,QAAQ,KAAI;AAC/B,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,mBAAmB,QAAQ,cAAa;AAEjD,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,OAAOC,aAAa,aAAY;AAEhC,OAAO,eAAeC,cACpBC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,mBAAwC,EACxCC,SAAoB;IAEpB,IAAIC;IAMJ,IAAI;QACFA,cAAc,IAAIb,OAAOc,QAAQC,OAAO,CAAC,0BAA0B;YACjEC,gBAAgB;gBAAC;aAAe;YAChCC,YAAY;YACZN;YACAO,YAAY;QACd;QAIA,MAAMC,WAAW,AAACT,CAAAA,kBAAkBP,mBAAkB,EAAGiB,MAAM,CAC7D,CAACC,KAAeC;YACd,MAAMC,UAAUrB,KAAKM,KAAKc;YAC1B,IAAI,CAACrB,WAAWsB,UAAU,OAAOF;YACjCA,IAAIG,IAAI,CAACD;YACT,OAAOF;QACT,GACA,EAAE;QAGJ,MAAMI,cAAc,OAAMZ,+BAAAA,YAAaa,YAAY,CAAClB,KAAKW,UAAU;YACjEQ,iBAAiB;YACjBC,eAAe;gBACbnB;YACF;QACF;QACA,MAAMoB,aACJ,OAAOJ,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaK,MAAM;QAErE,IAAI,OAAOL,gBAAgB,aAAYA,+BAAAA,YAAaM,SAAS,GAAE;YAC7DnB,UAAUoB,MAAM,CACd5B,wBAAwB;gBACtB,GAAGqB,YAAYM,SAAS;gBACxBE,WAAW;YACb;QAEJ;QAEA,IAAI,OAAOR,gBAAgB,aAAYA,+BAAAA,YAAanB,OAAO,KAAIuB,YAAY;YACzE,MAAMjB,UAAUsB,KAAK;YACrB,MAAM,qBAA4B,CAA5B,IAAI7B,aAAawB,aAAjB,qBAAA;uBAAA;4BAAA;8BAAA;YAA2B;QACnC;QAEA,IAAIA,YAAY;YACdM,QAAQC,GAAG,CAACP;QACd;IACF,EAAE,OAAOQ,KAAK;QACZ,IAAI/B,QAAQ+B,MAAM;YAChB,IAAIA,IAAIC,IAAI,KAAK,kBAAkBD,eAAehC,cAAc;gBAC9D8B,QAAQI,KAAK,CAACxC,IAAI;gBAClBoC,QAAQI,KAAK,CAACF,IAAIG,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf,OAAO,IAAIL,IAAIC,IAAI,KAAK,cAAc;gBACpCH,QAAQI,KAAK,CAACF,IAAIG,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf;QACF;QACA,MAAML;IACR,SAAU;QACR,IAAI;YACFxB,+BAAAA,YAAa8B,GAAG;QAClB,EAAE,OAAM,CAAC;IACX;AACF"}