{"version": 3, "sources": ["../../../src/client/compat/router.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { RouterContext } from '../../shared/lib/router-context.shared-runtime'\nimport type { NextRouter } from '../router'\n\n/**\n * useRouter from `next/compat/router` is designed to assist developers\n * migrating from `pages/` to `app/`. Unlike `next/router`, this hook does not\n * throw when the `NextRouter` is not mounted, and instead returns `null`. The\n * more concrete return type here lets developers use this hook within\n * components that could be shared between both `app/` and `pages/` and handle\n * to the case where the router is not mounted.\n *\n * @returns The `NextRouter` instance if it's available, otherwise `null`.\n */\nexport function useRouter(): NextRouter | null {\n  return useContext(RouterContext)\n}\n"], "names": ["useContext", "RouterContext", "useRouter"], "mappings": "AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,aAAa,QAAQ,iDAAgD;AAG9E;;;;;;;;;CASC,GACD,OAAO,SAASC;IACd,OAAOF,WAAWC;AACpB"}