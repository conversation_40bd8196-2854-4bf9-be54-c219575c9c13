import { AutoFocus } from "../../autoFocus.js";
import { GravityQualifier } from "../GravityQualifier.js";
/**
 * @description The class for the autoGravity builder
 * @memberOf Qualifiers.Gravity
 * @extends {Qualifiers.Gravity.GravityQualifier}
 */
declare class AutoGravity extends GravityQualifier {
    constructor();
    /**
     * @description Autofocuses on objects, allowing their priority within the algorithm to be configured.
     * @param {AutoFocus} AutoFocusObjects
     */
    autoFocus(...AutoFocusObjects: AutoFocus[]): this;
}
export { AutoGravity };
