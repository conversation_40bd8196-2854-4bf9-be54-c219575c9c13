{"version": 3, "sources": ["../../src/client/with-router.tsx"], "sourcesContent": ["import React, { type JSX } from 'react'\nimport type {\n  BaseContext,\n  NextComponentType,\n  NextPageContext,\n} from '../shared/lib/utils'\nimport type { NextRouter } from './router'\nimport { useRouter } from './router'\n\nexport type WithRouterProps = {\n  router: NextRouter\n}\n\nexport type ExcludeRouterProps<P> = Pick<\n  P,\n  Exclude<keyof P, keyof WithRouterProps>\n>\n\nexport default function withRouter<\n  P extends WithRouterProps,\n  C extends BaseContext = NextPageContext,\n>(\n  ComposedComponent: NextComponentType<C, any, P>\n): React.ComponentType<ExcludeRouterProps<P>> {\n  function WithRouterWrapper(props: any): JSX.Element {\n    return <ComposedComponent router={useRouter()} {...props} />\n  }\n\n  WithRouterWrapper.getInitialProps = ComposedComponent.getInitialProps\n  // This is needed to allow checking for custom getInitialProps in _app\n  ;(WithRouterWrapper as any).origGetInitialProps = (\n    ComposedComponent as any\n  ).origGetInitialProps\n  if (process.env.NODE_ENV !== 'production') {\n    const name =\n      ComposedComponent.displayName || ComposedComponent.name || 'Unknown'\n    WithRouterWrapper.displayName = `withRouter(${name})`\n  }\n\n  return WithRouterWrapper\n}\n"], "names": ["React", "useRouter", "with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "router", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": ";AAAA,OAAOA,WAAyB,QAAO;AAOvC,SAASC,SAAS,QAAQ,WAAU;AAWpC,eAAe,SAASC,WAItBC,iBAA+C;IAE/C,SAASC,kBAAkBC,KAAU;QACnC,qBAAO,KAACF;YAAkBG,QAAQL;YAAc,GAAGI,KAAK;;IAC1D;IAEAD,kBAAkBG,eAAe,GAAGJ,kBAAkBI,eAAe;IAEnEH,kBAA0BI,mBAAmB,GAAG,AAChDL,kBACAK,mBAAmB;IACrB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,OACJT,kBAAkBU,WAAW,IAAIV,kBAAkBS,IAAI,IAAI;QAC7DR,kBAAkBS,WAAW,GAAG,AAAC,gBAAaD,OAAK;IACrD;IAEA,OAAOR;AACT"}