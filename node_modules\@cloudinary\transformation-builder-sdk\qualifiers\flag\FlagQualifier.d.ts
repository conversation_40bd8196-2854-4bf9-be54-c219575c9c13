import { QualifierValue } from "../../internal/qualifier/QualifierValue.js";
import { Qualifier } from "../../internal/qualifier/Qualifier.js";
/**
 * @memberOf Qualifiers.Flag
 * @extends {SDK.Qualifier}
 * @description the FlagQualifier class
 */
declare class FlagQualifier extends Qualifier {
    flagValue: FlagQualifier | string;
    constructor(flagType?: QualifierValue | QualifierValue[] | number | number[] | string | string[], flagValue?: FlagQualifier | string);
    toString(): string;
    getFlagValue(): string;
}
export { FlagQualifier };
