'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth, hasPermission, type AuthUser } from '@/lib/auth'
import type { User } from '@supabase/supabase-js'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ user: AuthUser | null; error: string | null }>
  signUp: (email: string, password: string, fullName: string, role?: string) => Promise<{ user: User | null; error: string | null }>
  signOut: () => Promise<{ error: string | null }>
  resetPassword: (email: string) => Promise<{ error: string | null }>
  updatePassword: (password: string) => Promise<{ error: string | null }>
  updateProfile: (updates: any) => Promise<{ error: string | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (session?.user) {
          const profile = await getAdminProfile(session.user.id)
          setUser({ ...session.user, profile } as AuthUser)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          if (session?.user) {
            const profile = await getAdminProfile(session.user.id)
            setUser({ ...session.user, profile } as AuthUser)
          } else {
            setUser(null)
          }
        } catch (error) {
          console.error('Error in auth state change:', error)
          setUser(null)
        } finally {
          setLoading(false)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      const result = await auth.signIn(email, password)
      if (result.user) {
        setUser(result.user)
      }
      return result
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, fullName: string, role?: string) => {
    setLoading(true)
    try {
      return await auth.signUp(email, password, fullName, role)
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      const result = await auth.signOut()
      setUser(null)
      return result
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    return await auth.resetPassword(email)
  }

  const updatePassword = async (password: string) => {
    return await auth.updatePassword(password)
  }

  const updateProfile = async (updates: any) => {
    const result = await auth.updateProfile(updates)
    
    // Refresh user data if update was successful
    if (!result.error && user) {
      const profile = await getAdminProfile(user.id)
      setUser({ ...user, profile } as AuthUser)
    }
    
    return result
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for checking permissions
export function usePermissions() {
  const { user } = useAuth()

  const hasRole = (requiredRole: string | string[]): boolean => {
    if (!user?.profile) return false
    
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    return roles.includes(user.profile.role)
  }

  const canPerformAction = (action: string): boolean => {
    if (!user?.profile) return false

    const { role } = user.profile

    const permissions = {
      super_admin: [
        'create_product', 'edit_product', 'delete_product',
        'create_customer', 'edit_customer', 'delete_customer',
        'create_debt', 'edit_debt', 'delete_debt',
        'process_payment', 'edit_payment', 'delete_payment',
        'view_analytics', 'manage_users', 'system_settings'
      ],
      admin: [
        'create_product', 'edit_product',
        'create_customer', 'edit_customer',
        'create_debt', 'edit_debt',
        'process_payment', 'edit_payment',
        'view_analytics'
      ],
      cashier: [
        'create_debt', 'process_payment', 'view_analytics'
      ]
    }

    return permissions[role]?.includes(action) || false
  }

  const isSuperAdmin = (): boolean => hasRole('super_admin')
  const isAdmin = (): boolean => hasRole(['super_admin', 'admin'])
  const isCashier = (): boolean => hasRole('cashier')

  return {
    user,
    hasRole,
    canPerformAction,
    isSuperAdmin,
    isAdmin,
    isCashier
  }
}
