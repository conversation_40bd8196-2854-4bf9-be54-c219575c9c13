{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth, usePermissions } from '@/components/providers/AuthProvider'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: string | string[]\n  requiredPermission?: string\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  requiredPermission,\n  fallback\n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const { hasRole, canPerformAction } = usePermissions()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      // Redirect to login if not authenticated\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      // Check if user has required role\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n\n      // Check if user has required permission\n      if (requiredPermission && !canPerformAction(requiredPermission)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [user, loading, router, requiredRole, requiredPermission, hasRole, canPerformAction])\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show fallback or nothing if not authenticated\n  if (!user) {\n    return fallback || null\n  }\n\n  // Check role requirements\n  if (requiredRole && !hasRole(requiredRole)) {\n    return fallback || null\n  }\n\n  // Check permission requirements\n  if (requiredPermission && !canPerformAction(requiredPermission)) {\n    return fallback || null\n  }\n\n  // Render children if all checks pass\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withProtectedRoute<P extends object>(\n  WrappedComponent: React.ComponentType<P>,\n  options: {\n    requiredRole?: string | string[]\n    requiredPermission?: string\n  } = {}\n) {\n  return function ProtectedComponent(props: P) {\n    return (\n      <ProtectedRoute\n        requiredRole={options.requiredRole}\n        requiredPermission={options.requiredPermission}\n      >\n        <WrappedComponent {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Component for role-based rendering\ninterface RoleGuardProps {\n  children: React.ReactNode\n  requiredRole?: string | string[]\n  requiredPermission?: string\n  fallback?: React.ReactNode\n}\n\nexport function RoleGuard({\n  children,\n  requiredRole,\n  requiredPermission,\n  fallback = null\n}: RoleGuardProps) {\n  const { hasRole, canPerformAction } = usePermissions()\n\n  // Check role requirements\n  if (requiredRole && !hasRole(requiredRole)) {\n    return <>{fallback}</>\n  }\n\n  // Check permission requirements\n  if (requiredPermission && !canPerformAction(requiredPermission)) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n\n// Hook for conditional rendering based on permissions\nexport function useConditionalRender() {\n  const { hasRole, canPerformAction } = usePermissions()\n\n  const renderIf = (\n    condition: boolean | (() => boolean),\n    component: React.ReactNode,\n    fallback: React.ReactNode = null\n  ) => {\n    const shouldRender = typeof condition === 'function' ? condition() : condition\n    return shouldRender ? component : fallback\n  }\n\n  const renderIfRole = (\n    requiredRole: string | string[],\n    component: React.ReactNode,\n    fallback: React.ReactNode = null\n  ) => {\n    return renderIf(() => hasRole(requiredRole), component, fallback)\n  }\n\n  const renderIfPermission = (\n    permission: string,\n    component: React.ReactNode,\n    fallback: React.ReactNode = null\n  ) => {\n    return renderIf(() => canPerformAction(permission), component, fallback)\n  }\n\n  return {\n    renderIf,\n    renderIfRole,\n    renderIfPermission\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,kBAAkB,EAClB,QAAQ,EACY;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,yCAAyC;gBACzC,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,kCAAkC;gBAClC,IAAI,gBAAgB,CAAC,QAAQ,eAAe;oBAC1C,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,wCAAwC;gBACxC,IAAI,sBAAsB,CAAC,iBAAiB,qBAAqB;oBAC/D,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;mCAAG;QAAC;QAAM;QAAS;QAAQ;QAAc;QAAoB;QAAS;KAAiB;IAEvF,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,gDAAgD;IAChD,IAAI,CAAC,MAAM;QACT,OAAO,YAAY;IACrB;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,YAAY;IACrB;IAEA,gCAAgC;IAChC,IAAI,sBAAsB,CAAC,iBAAiB,qBAAqB;QAC/D,OAAO,YAAY;IACrB;IAEA,qCAAqC;IACrC,qBAAO;kBAAG;;AACZ;GA7DgB;;QAMY,kJAAA,CAAA,UAAO;QACK,kJAAA,CAAA,iBAAc;QACrC,qIAAA,CAAA,YAAS;;;KARV;AAgET,SAAS,mBACd,gBAAwC,EACxC,UAGI,CAAC,CAAC;IAEN,OAAO,SAAS,mBAAmB,KAAQ;QACzC,qBACE,6LAAC;YACC,cAAc,QAAQ,YAAY;YAClC,oBAAoB,QAAQ,kBAAkB;sBAE9C,cAAA,6LAAC;gBAAkB,GAAG,KAAK;;;;;;;;;;;IAGjC;AACF;AAUO,SAAS,UAAU,EACxB,QAAQ,EACR,YAAY,EACZ,kBAAkB,EAClB,WAAW,IAAI,EACA;;IACf,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEnD,0BAA0B;IAC1B,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,qBAAO;sBAAG;;IACZ;IAEA,gCAAgC;IAChC,IAAI,sBAAsB,CAAC,iBAAiB,qBAAqB;QAC/D,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;IAnBgB;;QAMwB,kJAAA,CAAA,iBAAc;;;MANtC;AAsBT,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEnD,MAAM,WAAW,CACf,WACA,WACA,WAA4B,IAAI;QAEhC,MAAM,eAAe,OAAO,cAAc,aAAa,cAAc;QACrE,OAAO,eAAe,YAAY;IACpC;IAEA,MAAM,eAAe,CACnB,cACA,WACA,WAA4B,IAAI;QAEhC,OAAO,SAAS,IAAM,QAAQ,eAAe,WAAW;IAC1D;IAEA,MAAM,qBAAqB,CACzB,YACA,WACA,WAA4B,IAAI;QAEhC,OAAO,SAAS,IAAM,iBAAiB,aAAa,WAAW;IACjE;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;IAjCgB;;QACwB,kJAAA,CAAA,iBAAc", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/utils/cn'\n\ninterface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            {label}\n          </label>\n        )}\n        <input\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-red-500 focus-visible:ring-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,qVACA,SAAS,6CACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/ui/DataTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from './Button'\nimport { Input } from './Input'\nimport { cn } from '@/utils/cn'\n\ninterface Column<T> {\n  key: keyof T | string\n  header: string\n  render?: (item: T) => React.ReactNode\n  sortable?: boolean\n  className?: string\n}\n\ninterface DataTableProps<T> {\n  data: T[]\n  columns: Column<T>[]\n  loading?: boolean\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n  onPageChange?: (page: number) => void\n  onSearch?: (search: string) => void\n  onSort?: (column: string, direction: 'asc' | 'desc') => void\n  searchPlaceholder?: string\n  emptyMessage?: string\n  actions?: (item: T) => React.ReactNode\n}\n\nexport function DataTable<T extends Record<string, any>>({\n  data,\n  columns,\n  loading = false,\n  pagination,\n  onPageChange,\n  onSearch,\n  onSort,\n  searchPlaceholder = 'Search...',\n  emptyMessage = 'No data found',\n  actions\n}: DataTableProps<T>) {\n  const [search, setSearch] = useState('')\n  const [sortColumn, setSortColumn] = useState<string>('')\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')\n\n  const handleSearch = (value: string) => {\n    setSearch(value)\n    onSearch?.(value)\n  }\n\n  const handleSort = (column: string) => {\n    if (!onSort) return\n\n    let direction: 'asc' | 'desc' = 'asc'\n    if (sortColumn === column && sortDirection === 'asc') {\n      direction = 'desc'\n    }\n\n    setSortColumn(column)\n    setSortDirection(direction)\n    onSort(column, direction)\n  }\n\n  const renderCell = (item: T, column: Column<T>) => {\n    if (column.render) {\n      return column.render(item)\n    }\n\n    const value = item[column.key as keyof T]\n    return value?.toString() || '-'\n  }\n\n  const renderPagination = () => {\n    if (!pagination || !onPageChange) return null\n\n    const { page, totalPages } = pagination\n    const pages = []\n    const maxVisiblePages = 5\n\n    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2))\n    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)\n\n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1)\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i)\n    }\n\n    return (\n      <div className=\"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200\">\n        <div className=\"flex items-center text-sm text-gray-700\">\n          <span>\n            Showing {((page - 1) * pagination.limit) + 1} to{' '}\n            {Math.min(page * pagination.limit, pagination.total)} of{' '}\n            {pagination.total} results\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(page - 1)}\n            disabled={page <= 1}\n          >\n            Previous\n          </Button>\n          \n          {pages.map(pageNum => (\n            <Button\n              key={pageNum}\n              variant={pageNum === page ? 'primary' : 'outline'}\n              size=\"sm\"\n              onClick={() => onPageChange(pageNum)}\n            >\n              {pageNum}\n            </Button>\n          ))}\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(page + 1)}\n            disabled={page >= totalPages}\n          >\n            Next\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n      {/* Search Bar */}\n      {onSearch && (\n        <div className=\"p-4 border-b border-gray-200\">\n          <Input\n            placeholder={searchPlaceholder}\n            value={search}\n            onChange={(e) => handleSearch(e.target.value)}\n            className=\"max-w-sm\"\n          />\n        </div>\n      )}\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              {columns.map((column, index) => (\n                <th\n                  key={index}\n                  className={cn(\n                    'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',\n                    column.sortable && 'cursor-pointer hover:bg-gray-100',\n                    column.className\n                  )}\n                  onClick={() => column.sortable && handleSort(column.key as string)}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>{column.header}</span>\n                    {column.sortable && (\n                      <svg\n                        className={cn(\n                          'w-4 h-4',\n                          sortColumn === column.key\n                            ? 'text-gray-900'\n                            : 'text-gray-400'\n                        )}\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        {sortColumn === column.key && sortDirection === 'desc' ? (\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                        ) : (\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n                        )}\n                      </svg>\n                    )}\n                  </div>\n                </th>\n              ))}\n              {actions && (\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              )}\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {loading ? (\n              <tr>\n                <td colSpan={columns.length + (actions ? 1 : 0)} className=\"px-6 py-12 text-center\">\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n                    <span className=\"ml-2 text-gray-500\">Loading...</span>\n                  </div>\n                </td>\n              </tr>\n            ) : data.length === 0 ? (\n              <tr>\n                <td colSpan={columns.length + (actions ? 1 : 0)} className=\"px-6 py-12 text-center text-gray-500\">\n                  {emptyMessage}\n                </td>\n              </tr>\n            ) : (\n              data.map((item, index) => (\n                <tr key={index} className=\"hover:bg-gray-50\">\n                  {columns.map((column, colIndex) => (\n                    <td\n                      key={colIndex}\n                      className={cn(\n                        'px-6 py-4 whitespace-nowrap text-sm text-gray-900',\n                        column.className\n                      )}\n                    >\n                      {renderCell(item, column)}\n                    </td>\n                  ))}\n                  {actions && (\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      {actions(item)}\n                    </td>\n                  )}\n                </tr>\n              ))\n            )}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Pagination */}\n      {renderPagination()}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAiCO,SAAS,UAAyC,EACvD,IAAI,EACJ,OAAO,EACP,UAAU,KAAK,EACf,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,oBAAoB,WAAW,EAC/B,eAAe,eAAe,EAC9B,OAAO,EACW;;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,WAAW;IACb;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,QAAQ;QAEb,IAAI,YAA4B;QAChC,IAAI,eAAe,UAAU,kBAAkB,OAAO;YACpD,YAAY;QACd;QAEA,cAAc;QACd,iBAAiB;QACjB,OAAO,QAAQ;IACjB;IAEA,MAAM,aAAa,CAAC,MAAS;QAC3B,IAAI,OAAO,MAAM,EAAE;YACjB,OAAO,OAAO,MAAM,CAAC;QACvB;QAEA,MAAM,QAAQ,IAAI,CAAC,OAAO,GAAG,CAAY;QACzC,OAAO,OAAO,cAAc;IAC9B;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc,CAAC,cAAc,OAAO;QAEzC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG;QAC7B,MAAM,QAAQ,EAAE;QAChB,MAAM,kBAAkB;QAExB,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,kBAAkB;QAChE,IAAI,UAAU,KAAK,GAAG,CAAC,YAAY,YAAY,kBAAkB;QAEjE,IAAI,UAAU,YAAY,IAAI,iBAAiB;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,kBAAkB;QACtD;QAEA,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;4BAAK;4BACM,CAAC,OAAO,CAAC,IAAI,WAAW,KAAK,GAAI;4BAAE;4BAAI;4BAChD,KAAK,GAAG,CAAC,OAAO,WAAW,KAAK,EAAE,WAAW,KAAK;4BAAE;4BAAI;4BACxD,WAAW,KAAK;4BAAC;;;;;;;;;;;;8BAItB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,OAAO;4BACnC,UAAU,QAAQ;sCACnB;;;;;;wBAIA,MAAM,GAAG,CAAC,CAAA,wBACT,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,YAAY,OAAO,YAAY;gCACxC,MAAK;gCACL,SAAS,IAAM,aAAa;0CAE3B;+BALI;;;;;sCAST,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,OAAO;4BACnC,UAAU,QAAQ;sCACnB;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBACJ,aAAa;oBACb,OAAO;oBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oBAC5C,WAAU;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;oCACE,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4CAEC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,kFACA,OAAO,QAAQ,IAAI,oCACnB,OAAO,SAAS;4CAElB,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,GAAG;sDAEvD,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,OAAO,MAAM;;;;;;oDACnB,OAAO,QAAQ,kBACd,6LAAC;wDACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,WACA,eAAe,OAAO,GAAG,GACrB,kBACA;wDAEN,MAAK;wDACL,QAAO;wDACP,SAAQ;kEAEP,eAAe,OAAO,GAAG,IAAI,kBAAkB,uBAC9C,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;iFAErE,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;2CAzBxE;;;;;oCAgCR,yBACC,6LAAC;wCAAG,WAAU;kDAAkF;;;;;;;;;;;;;;;;;sCAMtG,6LAAC;4BAAM,WAAU;sCACd,wBACC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;oCAAG,WAAU;8CACzD,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;uCAIzC,KAAK,MAAM,KAAK,kBAClB,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;oCAAG,WAAU;8CACxD;;;;;;;;;;uCAIL,KAAK,GAAG,CAAC,CAAC,MAAM,sBACd,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,GAAG,CAAC,CAAC,QAAQ,yBACpB,6LAAC;gDAEC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,qDACA,OAAO,SAAS;0DAGjB,WAAW,MAAM;+CANb;;;;;wCASR,yBACC,6LAAC;4CAAG,WAAU;sDACX,QAAQ;;;;;;;mCAdN;;;;;;;;;;;;;;;;;;;;;YAyBlB;;;;;;;AAGP;GAnNgB;KAAA", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/utils/cn'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-2xl font-semibold leading-none tracking-tight', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-gray-500', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/lib/services/productService.ts"], "sourcesContent": ["import { supabase, queries } from '@/lib/supabase'\nimport type { Product, Category, ProductFormData, ProductFilters, PaginationParams, PaginatedResponse } from '@/types'\n\nexport const productService = {\n  // Get all products with pagination and filters\n  getProducts: async (\n    params: PaginationParams & ProductFilters = { page: 1, limit: 10 }\n  ): Promise<PaginatedResponse<Product>> => {\n    const { page, limit, search, category_id, is_active, low_stock, price_min, price_max, sort_by = 'name', sort_order = 'asc' } = params\n    \n    let query = supabase\n      .from('products')\n      .select(`\n        *,\n        category:categories(*)\n      `, { count: 'exact' })\n\n    // Apply filters\n    if (search) {\n      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,barcode.ilike.%${search}%`)\n    }\n    \n    if (category_id) {\n      query = query.eq('category_id', category_id)\n    }\n    \n    if (typeof is_active === 'boolean') {\n      query = query.eq('is_active', is_active)\n    }\n    \n    if (low_stock) {\n      query = query.lte('stock_quantity', 10)\n    }\n    \n    if (price_min !== undefined) {\n      query = query.gte('price', price_min)\n    }\n    \n    if (price_max !== undefined) {\n      query = query.lte('price', price_max)\n    }\n\n    // Apply sorting\n    query = query.order(sort_by, { ascending: sort_order === 'asc' })\n\n    // Apply pagination\n    const from = (page - 1) * limit\n    const to = from + limit - 1\n    query = query.range(from, to)\n\n    const { data, error, count } = await query\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return {\n      data: data || [],\n      pagination: {\n        page,\n        limit,\n        total: count || 0,\n        totalPages: Math.ceil((count || 0) / limit)\n      }\n    }\n  },\n\n  // Get single product by ID\n  getProduct: async (id: string): Promise<Product | null> => {\n    const { data, error } = await supabase\n      .from('products')\n      .select(`\n        *,\n        category:categories(*)\n      `)\n      .eq('id', id)\n      .single()\n\n    if (error) {\n      if (error.code === 'PGRST116') return null // Not found\n      throw new Error(error.message)\n    }\n\n    return data\n  },\n\n  // Create new product\n  createProduct: async (productData: ProductFormData): Promise<Product> => {\n    const { image, ...data } = productData\n\n    const { data: product, error } = await supabase\n      .from('products')\n      .insert([data])\n      .select(`\n        *,\n        category:categories(*)\n      `)\n      .single()\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return product\n  },\n\n  // Update product\n  updateProduct: async (id: string, productData: Partial<ProductFormData>): Promise<Product> => {\n    const { image, ...data } = productData\n\n    const { data: product, error } = await supabase\n      .from('products')\n      .update(data)\n      .eq('id', id)\n      .select(`\n        *,\n        category:categories(*)\n      `)\n      .single()\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return product\n  },\n\n  // Delete product\n  deleteProduct: async (id: string): Promise<void> => {\n    const { error } = await supabase\n      .from('products')\n      .delete()\n      .eq('id', id)\n\n    if (error) {\n      throw new Error(error.message)\n    }\n  },\n\n  // Update product stock\n  updateStock: async (id: string, quantity: number, reason: string = 'manual_adjustment'): Promise<Product> => {\n    // Get current product\n    const product = await productService.getProduct(id)\n    if (!product) {\n      throw new Error('Product not found')\n    }\n\n    const previousStock = product.stock_quantity\n    const newStock = quantity\n\n    // Update product stock\n    const { data: updatedProduct, error: updateError } = await supabase\n      .from('products')\n      .update({ stock_quantity: newStock })\n      .eq('id', id)\n      .select(`\n        *,\n        category:categories(*)\n      `)\n      .single()\n\n    if (updateError) {\n      throw new Error(updateError.message)\n    }\n\n    // Record inventory movement\n    const { error: movementError } = await supabase\n      .from('inventory_movements')\n      .insert([{\n        product_id: id,\n        movement_type: newStock > previousStock ? 'in' : newStock < previousStock ? 'out' : 'adjustment',\n        quantity: Math.abs(newStock - previousStock),\n        previous_stock: previousStock,\n        new_stock: newStock,\n        reason\n      }])\n\n    if (movementError) {\n      console.error('Failed to record inventory movement:', movementError)\n    }\n\n    return updatedProduct\n  },\n\n  // Get low stock products\n  getLowStockProducts: async (threshold: number = 10): Promise<Product[]> => {\n    const { data, error } = await queries.getLowStockProducts(threshold)\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return data || []\n  },\n\n  // Bulk update products\n  bulkUpdateProducts: async (updates: Array<{ id: string; data: Partial<ProductFormData> }>): Promise<Product[]> => {\n    const results = await Promise.all(\n      updates.map(({ id, data }) => productService.updateProduct(id, data))\n    )\n    return results\n  },\n\n  // Get product inventory movements\n  getInventoryMovements: async (productId: string): Promise<any[]> => {\n    const { data, error } = await supabase\n      .from('inventory_movements')\n      .select('*')\n      .eq('product_id', productId)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return data || []\n  }\n}\n\n// Category service\nexport const categoryService = {\n  // Get all categories\n  getCategories: async (): Promise<Category[]> => {\n    const { data, error } = await supabase\n      .from('categories')\n      .select('*')\n      .order('name')\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return data || []\n  },\n\n  // Create category\n  createCategory: async (categoryData: { name: string; description?: string }): Promise<Category> => {\n    const { data, error } = await supabase\n      .from('categories')\n      .insert([categoryData])\n      .select()\n      .single()\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return data\n  },\n\n  // Update category\n  updateCategory: async (id: string, categoryData: { name?: string; description?: string }): Promise<Category> => {\n    const { data, error } = await supabase\n      .from('categories')\n      .update(categoryData)\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      throw new Error(error.message)\n    }\n\n    return data\n  },\n\n  // Delete category\n  deleteCategory: async (id: string): Promise<void> => {\n    const { error } = await supabase\n      .from('categories')\n      .delete()\n      .eq('id', id)\n\n    if (error) {\n      throw new Error(error.message)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,iBAAiB;IAC5B,+CAA+C;IAC/C,aAAa,OACX,SAA4C;QAAE,MAAM;QAAG,OAAO;IAAG,CAAC;QAElE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,MAAM,EAAE,aAAa,KAAK,EAAE,GAAG;QAE/H,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EAAE;YAAE,OAAO;QAAQ;QAEtB,gBAAgB;QAChB,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,CAAC,YAAY,EAAE,OAAO,qBAAqB,EAAE,OAAO,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACnG;QAEA,IAAI,aAAa;YACf,QAAQ,MAAM,EAAE,CAAC,eAAe;QAClC;QAEA,IAAI,OAAO,cAAc,WAAW;YAClC,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,IAAI,WAAW;YACb,QAAQ,MAAM,GAAG,CAAC,kBAAkB;QACtC;QAEA,IAAI,cAAc,WAAW;YAC3B,QAAQ,MAAM,GAAG,CAAC,SAAS;QAC7B;QAEA,IAAI,cAAc,WAAW;YAC3B,QAAQ,MAAM,GAAG,CAAC,SAAS;QAC7B;QAEA,gBAAgB;QAChB,QAAQ,MAAM,KAAK,CAAC,SAAS;YAAE,WAAW,eAAe;QAAM;QAE/D,mBAAmB;QACnB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,QAAQ;QAC1B,QAAQ,MAAM,KAAK,CAAC,MAAM;QAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAErC,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO;YACL,MAAM,QAAQ,EAAE;YAChB,YAAY;gBACV;gBACA;gBACA,OAAO,SAAS;gBAChB,YAAY,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;YACvC;QACF;IACF;IAEA,2BAA2B;IAC3B,YAAY,OAAO;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,KAAK,YAAY;;YACvD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,eAAe,OAAO;QACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG;QAE3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAK,EACb,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,MAAM;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,eAAe,OAAO,IAAY;QAChC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG;QAE3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,IACT,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,MAAM;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,eAAe,OAAO;QACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;IAEA,uBAAuB;IACvB,aAAa,OAAO,IAAY,UAAkB,SAAiB,mBAAmB;QACpF,sBAAsB;QACtB,MAAM,UAAU,MAAM,eAAe,UAAU,CAAC;QAChD,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,gBAAgB,QAAQ,cAAc;QAC5C,MAAM,WAAW;QAEjB,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAChE,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,gBAAgB;QAAS,GAClC,EAAE,CAAC,MAAM,IACT,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,MAAM;QAET,IAAI,aAAa;YACf,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,4BAA4B;QAC5B,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,uBACL,MAAM,CAAC;YAAC;gBACP,YAAY;gBACZ,eAAe,WAAW,gBAAgB,OAAO,WAAW,gBAAgB,QAAQ;gBACpF,UAAU,KAAK,GAAG,CAAC,WAAW;gBAC9B,gBAAgB;gBAChB,WAAW;gBACX;YACF;SAAE;QAEJ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,wCAAwC;QACxD;QAEA,OAAO;IACT;IAEA,yBAAyB;IACzB,qBAAqB,OAAO,YAAoB,EAAE;QAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;QAE1D,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,eAAe,aAAa,CAAC,IAAI;QAEjE,OAAO;IACT;IAEA,kCAAkC;IAClC,uBAAuB,OAAO;QAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,QAAQ,EAAE;IACnB;AACF;AAGO,MAAM,kBAAkB;IAC7B,qBAAqB;IACrB,eAAe;QACb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,QAAQ,EAAE;IACnB;IAEA,kBAAkB;IAClB,gBAAgB,OAAO;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,gBAAgB,OAAO,IAAY;QACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,gBAAgB,OAAO;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;AACF", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/utils/index.ts"], "sourcesContent": ["// Utility functions\n\nexport const formatDate = (date: string | Date): string => {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport const generateId = (): string => {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport const debounce = <T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;AAEb,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/app/products/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { usePermissions } from '@/components/providers/AuthProvider'\nimport { Button } from '@/components/ui/Button'\nimport { DataTable } from '@/components/ui/DataTable'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { productService, categoryService } from '@/lib/services/productService'\nimport { formatCurrency } from '@/utils'\nimport type { Product, Category, ProductFilters, PaginationParams } from '@/types'\n\nfunction ProductsContent() {\n  const { canPerformAction } = usePermissions()\n  const [products, setProducts] = useState<Product[]>([])\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    totalPages: 0\n  })\n  const [filters, setFilters] = useState<ProductFilters>({})\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    loadProducts()\n    loadCategories()\n  }, [pagination.page, pagination.limit, filters])\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true)\n      const params: PaginationParams & ProductFilters = {\n        page: pagination.page,\n        limit: pagination.limit,\n        ...filters\n      }\n      \n      const response = await productService.getProducts(params)\n      setProducts(response.data)\n      setPagination(response.pagination)\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Failed to load products')\n      console.error('Failed to load products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadCategories = async () => {\n    try {\n      const data = await categoryService.getCategories()\n      setCategories(data)\n    } catch (error) {\n      console.error('Failed to load categories:', error)\n    }\n  }\n\n  const handleSearch = (search: string) => {\n    setFilters(prev => ({ ...prev, search }))\n    setPagination(prev => ({ ...prev, page: 1 }))\n  }\n\n  const handleSort = (column: string, direction: 'asc' | 'desc') => {\n    setFilters(prev => ({ ...prev, sort_by: column, sort_order: direction }))\n  }\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }))\n  }\n\n  const handleDeleteProduct = async (product: Product) => {\n    if (!canPerformAction('delete_product')) {\n      alert('You do not have permission to delete products')\n      return\n    }\n\n    if (!confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n      return\n    }\n\n    try {\n      await productService.deleteProduct(product.id)\n      loadProducts() // Reload the list\n    } catch (error) {\n      alert(error instanceof Error ? error.message : 'Failed to delete product')\n    }\n  }\n\n  const columns = [\n    {\n      key: 'image_url',\n      header: 'Image',\n      render: (product: Product) => (\n        <div className=\"w-12 h-12 relative\">\n          {product.image_url ? (\n            <Image\n              src={product.image_url}\n              alt={product.name}\n              fill\n              className=\"object-cover rounded-md\"\n            />\n          ) : (\n            <div className=\"w-full h-full bg-gray-200 rounded-md flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n            </div>\n          )}\n        </div>\n      )\n    },\n    {\n      key: 'name',\n      header: 'Product Name',\n      sortable: true,\n      render: (product: Product) => (\n        <div>\n          <div className=\"font-medium text-gray-900\">{product.name}</div>\n          {product.net_weight && (\n            <div className=\"text-sm text-gray-500\">{product.net_weight}</div>\n          )}\n        </div>\n      )\n    },\n    {\n      key: 'category',\n      header: 'Category',\n      render: (product: Product) => (\n        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n          {product.category?.name || 'Uncategorized'}\n        </span>\n      )\n    },\n    {\n      key: 'price',\n      header: 'Price',\n      sortable: true,\n      render: (product: Product) => (\n        <span className=\"font-medium\">{formatCurrency(product.price, 'PHP')}</span>\n      )\n    },\n    {\n      key: 'stock_quantity',\n      header: 'Stock',\n      sortable: true,\n      render: (product: Product) => (\n        <span className={`font-medium ${\n          product.stock_quantity <= 10 \n            ? 'text-red-600' \n            : product.stock_quantity <= 20 \n            ? 'text-yellow-600' \n            : 'text-green-600'\n        }`}>\n          {product.stock_quantity}\n        </span>\n      )\n    },\n    {\n      key: 'is_active',\n      header: 'Status',\n      render: (product: Product) => (\n        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n          product.is_active \n            ? 'bg-green-100 text-green-800' \n            : 'bg-red-100 text-red-800'\n        }`}>\n          {product.is_active ? 'Active' : 'Inactive'}\n        </span>\n      )\n    }\n  ]\n\n  const renderActions = (product: Product) => (\n    <div className=\"flex items-center space-x-2\">\n      <Link href={`/products/${product.id}`}>\n        <Button variant=\"outline\" size=\"sm\">\n          View\n        </Button>\n      </Link>\n      {canPerformAction('edit_product') && (\n        <Link href={`/products/${product.id}/edit`}>\n          <Button variant=\"outline\" size=\"sm\">\n            Edit\n          </Button>\n        </Link>\n      )}\n      {canPerformAction('delete_product') && (\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handleDeleteProduct(product)}\n          className=\"text-red-600 hover:text-red-700\"\n        >\n          Delete\n        </Button>\n      )}\n    </div>\n  )\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Products</h1>\n          <p className=\"text-gray-600 mt-2\">Manage your store inventory</p>\n        </div>\n        {canPerformAction('create_product') && (\n          <Link href=\"/products/new\">\n            <Button>Add New Product</Button>\n          </Link>\n        )}\n      </div>\n\n      {/* Filters */}\n      <Card className=\"mb-6\">\n        <CardHeader>\n          <CardTitle>Filters</CardTitle>\n          <CardDescription>Filter products by category and status</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Category\n              </label>\n              <select\n                value={filters.category_id || ''}\n                onChange={(e) => setFilters(prev => ({ ...prev, category_id: e.target.value || undefined }))}\n                className=\"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Status\n              </label>\n              <select\n                value={filters.is_active?.toString() || ''}\n                onChange={(e) => setFilters(prev => ({ \n                  ...prev, \n                  is_active: e.target.value ? e.target.value === 'true' : undefined \n                }))}\n                className=\"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">All Status</option>\n                <option value=\"true\">Active</option>\n                <option value=\"false\">Inactive</option>\n              </select>\n            </div>\n\n            <div className=\"flex items-end\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={filters.low_stock || false}\n                  onChange={(e) => setFilters(prev => ({ ...prev, low_stock: e.target.checked || undefined }))}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700\">Low Stock Only</span>\n              </label>\n            </div>\n\n            <div className=\"flex items-end\">\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setFilters({})\n                  setPagination(prev => ({ ...prev, page: 1 }))\n                }}\n              >\n                Clear Filters\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-6\">\n          <p className=\"text-sm text-red-600\">{error}</p>\n        </div>\n      )}\n\n      <DataTable\n        data={products}\n        columns={columns}\n        loading={loading}\n        pagination={pagination}\n        onPageChange={handlePageChange}\n        onSearch={handleSearch}\n        onSort={handleSort}\n        searchPlaceholder=\"Search products...\"\n        emptyMessage=\"No products found\"\n        actions={renderActions}\n      />\n    </div>\n  )\n}\n\nexport default function ProductsPage() {\n  return (\n    <ProtectedRoute requiredPermission=\"view_analytics\">\n      <ProductsContent />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAcA,SAAS;;IACP,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACxD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YACA;QACF;oCAAG;QAAC,WAAW,IAAI;QAAE,WAAW,KAAK;QAAE;KAAQ;IAE/C,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,SAA4C;gBAChD,MAAM,WAAW,IAAI;gBACrB,OAAO,WAAW,KAAK;gBACvB,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,2IAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;YAClD,YAAY,SAAS,IAAI;YACzB,cAAc,SAAS,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,2IAAA,CAAA,kBAAe,CAAC,aAAa;YAChD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAO,CAAC;QACvC,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,MAAM,aAAa,CAAC,QAAgB;QAClC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAQ,YAAY;YAAU,CAAC;IACzE;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;IAC1C;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,iBAAiB,mBAAmB;YACvC,MAAM;YACN;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,iCAAiC,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG;YAClE;QACF;QAEA,IAAI;YACF,MAAM,2IAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC7C,eAAe,kBAAkB;;QACnC,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjD;IACF;IAEA,MAAM,UAAU;QACd;YACE,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,wBACP,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,SAAS,iBAChB,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,SAAS;wBACtB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC5E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;QAMjF;QACA;YACE,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,wBACP,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCAA6B,QAAQ,IAAI;;;;;;wBACvD,QAAQ,UAAU,kBACjB,6LAAC;4BAAI,WAAU;sCAAyB,QAAQ,UAAU;;;;;;;;;;;;QAIlE;QACA;YACE,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,wBACP,6LAAC;oBAAK,WAAU;8BACb,QAAQ,QAAQ,EAAE,QAAQ;;;;;;QAGjC;QACA;YACE,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,wBACP,6LAAC;oBAAK,WAAU;8BAAe,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK,EAAE;;;;;;QAEjE;QACA;YACE,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,wBACP,6LAAC;oBAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,cAAc,IAAI,KACtB,iBACA,QAAQ,cAAc,IAAI,KAC1B,oBACA,kBACJ;8BACC,QAAQ,cAAc;;;;;;QAG7B;QACA;YACE,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,wBACP,6LAAC;oBAAK,WAAW,CAAC,wEAAwE,EACxF,QAAQ,SAAS,GACb,gCACA,2BACJ;8BACC,QAAQ,SAAS,GAAG,WAAW;;;;;;QAGtC;KACD;IAED,MAAM,gBAAgB,CAAC,wBACrB,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;8BACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;gBAIrC,iBAAiB,iCAChB,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;8BACxC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;gBAKvC,iBAAiB,mCAChB,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,oBAAoB;oBACnC,WAAU;8BACX;;;;;;;;;;;;IAOP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAEnC,iBAAiB,mCAChB,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sCAAC;;;;;;;;;;;;;;;;;0BAMd,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,QAAQ,WAAW,IAAI;4CAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK,IAAI;oDAAU,CAAC;4CAC1F,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAO9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,QAAQ,SAAS,EAAE,cAAc;4CACxC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDACnC,GAAG,IAAI;wDACP,WAAW,EAAE,MAAM,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK,KAAK,SAAS;oDAC1D,CAAC;4CACD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,SAAS,QAAQ,SAAS,IAAI;gDAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,EAAE,MAAM,CAAC,OAAO,IAAI;wDAAU,CAAC;gDAC1F,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAI5C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,WAAW,CAAC;4CACZ,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM;gDAAE,CAAC;wCAC7C;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQR,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAIzC,6LAAC,wIAAA,CAAA,YAAS;gBACR,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,mBAAkB;gBAClB,cAAa;gBACb,SAAS;;;;;;;;;;;;AAIjB;GAvSS;;QACsB,kJAAA,CAAA,iBAAc;;;KADpC;AAySM,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,iBAAc;QAAC,oBAAmB;kBACjC,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}