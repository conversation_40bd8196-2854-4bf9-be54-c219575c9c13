{"version": 3, "sources": ["../../../../src/server/app-render/rsc/taint.ts"], "sourcesContent": ["/*\n\nFiles in the rsc directory are meant to be packaged as part of the RSC graph using next-app-loader.\n\n*/\n\nimport * as React from 'react'\n\ntype Reference = object\ntype TaintableUniqueValue = string | bigint | ArrayBufferView\n\nfunction notImplemented() {\n  throw new Error('Taint can only be used with the taint flag.')\n}\n\nexport const taintObjectReference: (\n  message: string | undefined,\n  object: Reference\n) => void = process.env.__NEXT_EXPERIMENTAL_REACT\n  ? // @ts-ignore\n    React.experimental_taintObjectReference\n  : notImplemented\nexport const taintUniqueValue: (\n  message: string | undefined,\n  lifetime: Reference,\n  value: TaintableUniqueValue\n) => void = process.env.__NEXT_EXPERIMENTAL_REACT\n  ? // @ts-ignore\n    React.experimental_taintUniqueValue\n  : notImplemented\n"], "names": ["React", "notImplemented", "Error", "taintObjectReference", "process", "env", "__NEXT_EXPERIMENTAL_REACT", "experimental_taintObjectReference", "taintUniqueValue", "experimental_taintUniqueValue"], "mappings": "AAAA;;;;AAIA,GAEA,YAAYA,WAAW,QAAO;AAK9B,SAASC;IACP,MAAM,qBAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuD;AAC/D;AAEA,OAAO,MAAMC,uBAGDC,QAAQC,GAAG,CAACC,yBAAyB,GAE7CN,MAAMO,iCAAiC,GACvCN,eAAc;AAClB,OAAO,MAAMO,mBAIDJ,QAAQC,GAAG,CAACC,yBAAyB,GAE7CN,MAAMS,6BAA6B,GACnCR,eAAc"}