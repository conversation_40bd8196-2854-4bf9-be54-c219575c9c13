{"version": 3, "sources": ["../../../../src/client/components/router-reducer/set-cache-busting-search-param.ts"], "sourcesContent": ["'use client'\nimport { hexHash } from '../../../shared/lib/hash'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  NEXT_RSC_UNION_QUERY,\n} from '../app-router-headers'\nimport type { RequestHeaders } from './fetch-server-response'\n\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */\nexport const setCacheBustingSearchParam = (\n  url: URL,\n  headers: RequestHeaders\n): void => {\n  const uniqueCacheKey = hexHash(\n    [\n      headers[NEXT_ROUTER_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_STATE_TREE_HEADER],\n      headers[NEXT_URL],\n    ].join(',')\n  )\n\n  /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */\n  const existingSearch = url.search\n  const rawQuery = existingSearch.startsWith('?')\n    ? existingSearch.slice(1)\n    : existingSearch\n  const pairs = rawQuery.split('&').filter(Boolean)\n  pairs.push(`${NEXT_RSC_UNION_QUERY}=${uniqueCacheKey}`)\n  url.search = pairs.length ? `?${pairs.join('&')}` : ''\n}\n"], "names": ["setCacheBustingSearchParam", "url", "headers", "unique<PERSON><PERSON><PERSON><PERSON>", "hexHash", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "join", "existingSearch", "search", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "slice", "pairs", "split", "filter", "Boolean", "push", "NEXT_RSC_UNION_QUERY", "length"], "mappings": "AAAA;;;;;+BA2BaA;;;eAAAA;;;sBA1BW;kCAOjB;AAmBA,MAAMA,6BAA6B,CACxCC,KACAC;IAEA,MAAMC,iBAAiBC,IAAAA,aAAO,EAC5B;QACEF,OAAO,CAACG,6CAA2B,CAAC,IAAI;QACxCH,OAAO,CAACI,qDAAmC,CAAC,IAAI;QAChDJ,OAAO,CAACK,+CAA6B,CAAC;QACtCL,OAAO,CAACM,0BAAQ,CAAC;KAClB,CAACC,IAAI,CAAC;IAGT;;;;;;;;;;GAUC,GACD,MAAMC,iBAAiBT,IAAIU,MAAM;IACjC,MAAMC,WAAWF,eAAeG,UAAU,CAAC,OACvCH,eAAeI,KAAK,CAAC,KACrBJ;IACJ,MAAMK,QAAQH,SAASI,KAAK,CAAC,KAAKC,MAAM,CAACC;IACzCH,MAAMI,IAAI,CAAC,AAAGC,sCAAoB,GAAC,MAAGjB;IACtCF,IAAIU,MAAM,GAAGI,MAAMM,MAAM,GAAG,AAAC,MAAGN,MAAMN,IAAI,CAAC,OAAS;AACtD"}