/**
 * @description Contains functions to select the alignment of the text.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/layers#text_layer_options|Adding text overlays to images}
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_layers#text_layer_options|Adding text overlays to videos}
 * @memberOf Qualifiers
 * @namespace TextAlignment
 * @see To be used with {@link Qualifiers.TextStyle|Text Style}
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.TextAlignment
 */
declare function left(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.TextAlignment
 */
declare function right(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.TextAlignment
 */
declare function center(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.TextAlignment
 */
declare function start(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.TextAlignment
 */
declare function end(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.TextAlignment
 */
declare function justify(): string;
declare const TextAlignment: {
    left: typeof left;
    right: typeof right;
    center: typeof center;
    end: typeof end;
    justify: typeof justify;
    start: typeof start;
};
export { TextAlignment, left, right, center, end, justify, start };
