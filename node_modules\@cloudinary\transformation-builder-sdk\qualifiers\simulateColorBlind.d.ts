/**
 * @description Contains functions to select the type of color-blind condition to simulate.
 * <b>Learn more</b>: {@link https://cloudinary.com/blog/open_your_eyes_to_color_accessibility|Blog: Open your Eyes to Color Accessibility}
 * @memberOf Qualifiers
 * @namespace SimulateColorBlindValues
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function deuteranopia(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function protanopia(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function tritanopia(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function tritanomaly(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function deuteranomaly(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function coneMonochromacy(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
declare function rodMonochromacy(): string;
declare const SimulateColorBlind: {
    coneMonochromacy: typeof coneMonochromacy;
    deuteranomaly: typeof deuteranomaly;
    deuteranopia: typeof deuteranopia;
    protanopia: typeof protanopia;
    rodMonochromacy: typeof rodMonochromacy;
    tritanomaly: typeof tritanomaly;
    tritanopia: typeof tritanopia;
};
export { SimulateColorBlind, coneMonochromacy, deuteranomaly, deuteranopia, protanopia, rodMonochromacy, tritanomaly, tritanopia };
