/**
 * @description Contains functions to select the maximum number of pixels in the horizontal and vertical direction that are addressed.
 * @memberOf Qualifiers
 * @namespace ShakeStrength
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.ShakeStrength
 */
declare function pixels16(): 16;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ShakeStrength
 */
declare function pixels32(): 32;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ShakeStrength
 */
declare function pixels48(): 48;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ShakeStrength
 */
declare function pixels64(): 64;
declare const ShakeStrength: {
    pixels16: typeof pixels16;
    pixels32: typeof pixels32;
    pixels48: typeof pixels48;
    pixels64: typeof pixels64;
};
export { pixels16, pixels32, pixels48, pixels64, ShakeStrength };
