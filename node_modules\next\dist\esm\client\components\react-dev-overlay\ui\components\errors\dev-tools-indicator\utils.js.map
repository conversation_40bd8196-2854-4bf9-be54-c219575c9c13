{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.ts"], "sourcesContent": ["import { useEffect } from 'react'\n\nexport function useFocusTrap(\n  rootRef: React.RefObject<HTMLElement | null>,\n  triggerRef: React.RefObject<HTMLButtonElement | null> | null,\n  active: boolean,\n  onOpenFocus?: () => void\n) {\n  useEffect(() => {\n    let rootNode: HTMLElement | null = null\n\n    function onTab(e: KeyboardEvent) {\n      if (e.key !== 'Tab' || rootNode === null) {\n        return\n      }\n\n      const [firstFocusableNode, lastFocusableNode] =\n        getFocusableNodes(rootNode)\n      const activeElement = getActiveElement(rootNode)\n\n      if (e.shiftKey) {\n        if (activeElement === firstFocusableNode) {\n          lastFocusableNode?.focus()\n          e.preventDefault()\n        }\n      } else {\n        if (activeElement === lastFocusableNode) {\n          firstFocusableNode?.focus()\n          e.preventDefault()\n        }\n      }\n    }\n\n    const id = setTimeout(() => {\n      // Grab this on next tick to ensure the content is mounted\n      rootNode = rootRef.current\n      if (active) {\n        if (onOpenFocus) {\n          onOpenFocus()\n        } else {\n          rootNode?.focus()\n        }\n        rootNode?.addEventListener('keydown', onTab)\n      } else {\n        const activeElement = getActiveElement(rootNode)\n        // Only restore focus if the focus was previously on the content.\n        // This avoids us accidentally focusing on mount when the\n        // user could want to interact with their own app instead.\n        if (triggerRef && rootNode?.contains(activeElement)) {\n          triggerRef.current?.focus()\n        }\n      }\n    })\n\n    return () => {\n      clearTimeout(id)\n      rootNode?.removeEventListener('keydown', onTab)\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [active])\n}\n\nfunction getActiveElement(node: HTMLElement | null) {\n  const root = node?.getRootNode()\n  return root instanceof ShadowRoot\n    ? (root?.activeElement as HTMLElement)\n    : null\n}\n\nfunction getFocusableNodes(node: HTMLElement): [HTMLElement, HTMLElement] | [] {\n  const focusableElements = node.querySelectorAll(\n    'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n  )\n  if (!focusableElements) return []\n  return [\n    focusableElements![0] as HTMLElement,\n    focusableElements![focusableElements!.length - 1] as HTMLElement,\n  ]\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport function useClickOutside(\n  rootRef: React.RefObject<HTMLElement | null>,\n  triggerRef: React.RefObject<HTMLButtonElement | null>,\n  active: boolean,\n  close: () => void\n) {\n  useEffect(() => {\n    if (!active) {\n      return\n    }\n\n    function handleClickOutside(event: MouseEvent) {\n      if (\n        !(rootRef.current?.getBoundingClientRect()\n          ? event.clientX >= rootRef.current.getBoundingClientRect()!.left &&\n            event.clientX <= rootRef.current.getBoundingClientRect()!.right &&\n            event.clientY >= rootRef.current.getBoundingClientRect()!.top &&\n            event.clientY <= rootRef.current.getBoundingClientRect()!.bottom\n          : false) &&\n        !(triggerRef.current?.getBoundingClientRect()\n          ? event.clientX >= triggerRef.current.getBoundingClientRect()!.left &&\n            event.clientX <=\n              triggerRef.current.getBoundingClientRect()!.right &&\n            event.clientY >= triggerRef.current.getBoundingClientRect()!.top &&\n            event.clientY <= triggerRef.current.getBoundingClientRect()!.bottom\n          : false)\n      ) {\n        close()\n      }\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      if (event.key === 'Escape') {\n        close()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    document.addEventListener('keydown', handleKeyDown)\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.removeEventListener('keydown', handleKeyDown)\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [active])\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport const MENU_DURATION_MS = 200\nexport const MENU_CURVE = 'cubic-bezier(0.175, 0.885, 0.32, 1.1)'\n"], "names": ["useEffect", "useFocusTrap", "rootRef", "triggerRef", "active", "onOpenFocus", "rootNode", "onTab", "e", "key", "firstFocusableNode", "lastFocusableNode", "getFocusableNodes", "activeElement", "getActiveElement", "shift<PERSON>ey", "focus", "preventDefault", "id", "setTimeout", "current", "addEventListener", "contains", "clearTimeout", "removeEventListener", "node", "root", "getRootNode", "ShadowRoot", "focusableElements", "querySelectorAll", "length", "useClickOutside", "close", "handleClickOutside", "event", "getBoundingClientRect", "clientX", "left", "right", "clientY", "top", "bottom", "handleKeyDown", "document", "MENU_DURATION_MS", "MENU_CURVE"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AAEjC,OAAO,SAASC,aACdC,OAA4C,EAC5CC,UAA4D,EAC5DC,MAAe,EACfC,WAAwB;IAExBL,UAAU;QACR,IAAIM,WAA+B;QAEnC,SAASC,MAAMC,CAAgB;YAC7B,IAAIA,EAAEC,GAAG,KAAK,SAASH,aAAa,MAAM;gBACxC;YACF;YAEA,MAAM,CAACI,oBAAoBC,kBAAkB,GAC3CC,kBAAkBN;YACpB,MAAMO,gBAAgBC,iBAAiBR;YAEvC,IAAIE,EAAEO,QAAQ,EAAE;gBACd,IAAIF,kBAAkBH,oBAAoB;oBACxCC,qCAAAA,kBAAmBK,KAAK;oBACxBR,EAAES,cAAc;gBAClB;YACF,OAAO;gBACL,IAAIJ,kBAAkBF,mBAAmB;oBACvCD,sCAAAA,mBAAoBM,KAAK;oBACzBR,EAAES,cAAc;gBAClB;YACF;QACF;QAEA,MAAMC,KAAKC,WAAW;YACpB,0DAA0D;YAC1Db,WAAWJ,QAAQkB,OAAO;YAC1B,IAAIhB,QAAQ;gBACV,IAAIC,aAAa;oBACfA;gBACF,OAAO;oBACLC,4BAAAA,SAAUU,KAAK;gBACjB;gBACAV,4BAAAA,SAAUe,gBAAgB,CAAC,WAAWd;YACxC,OAAO;gBACL,MAAMM,gBAAgBC,iBAAiBR;gBACvC,iEAAiE;gBACjE,yDAAyD;gBACzD,0DAA0D;gBAC1D,IAAIH,eAAcG,4BAAAA,SAAUgB,QAAQ,CAACT,iBAAgB;wBACnDV;qBAAAA,sBAAAA,WAAWiB,OAAO,qBAAlBjB,oBAAoBa,KAAK;gBAC3B;YACF;QACF;QAEA,OAAO;YACLO,aAAaL;YACbZ,4BAAAA,SAAUkB,mBAAmB,CAAC,WAAWjB;QAC3C;IACA,uDAAuD;IACzD,GAAG;QAACH;KAAO;AACb;AAEA,SAASU,iBAAiBW,IAAwB;IAChD,MAAMC,OAAOD,wBAAAA,KAAME,WAAW;IAC9B,OAAOD,gBAAgBE,aAClBF,wBAAAA,KAAMb,aAAa,GACpB;AACN;AAEA,SAASD,kBAAkBa,IAAiB;IAC1C,MAAMI,oBAAoBJ,KAAKK,gBAAgB,CAC7C;IAEF,IAAI,CAACD,mBAAmB,OAAO,EAAE;IACjC,OAAO;QACLA,iBAAkB,CAAC,EAAE;QACrBA,iBAAkB,CAACA,kBAAmBE,MAAM,GAAG,EAAE;KAClD;AACH;AAEA,sFAAsF;AAEtF,OAAO,SAASC,gBACd9B,OAA4C,EAC5CC,UAAqD,EACrDC,MAAe,EACf6B,KAAiB;IAEjBjC,UAAU;QACR,IAAI,CAACI,QAAQ;YACX;QACF;QAEA,SAAS8B,mBAAmBC,KAAiB;gBAEvCjC,kBAMAC;YAPJ,IACE,CAAED,CAAAA,EAAAA,mBAAAA,QAAQkB,OAAO,qBAAflB,iBAAiBkC,qBAAqB,MACpCD,MAAME,OAAO,IAAInC,QAAQkB,OAAO,CAACgB,qBAAqB,GAAIE,IAAI,IAC9DH,MAAME,OAAO,IAAInC,QAAQkB,OAAO,CAACgB,qBAAqB,GAAIG,KAAK,IAC/DJ,MAAMK,OAAO,IAAItC,QAAQkB,OAAO,CAACgB,qBAAqB,GAAIK,GAAG,IAC7DN,MAAMK,OAAO,IAAItC,QAAQkB,OAAO,CAACgB,qBAAqB,GAAIM,MAAM,GAChE,KAAI,KACR,CAAEvC,CAAAA,EAAAA,sBAAAA,WAAWiB,OAAO,qBAAlBjB,oBAAoBiC,qBAAqB,MACvCD,MAAME,OAAO,IAAIlC,WAAWiB,OAAO,CAACgB,qBAAqB,GAAIE,IAAI,IACjEH,MAAME,OAAO,IACXlC,WAAWiB,OAAO,CAACgB,qBAAqB,GAAIG,KAAK,IACnDJ,MAAMK,OAAO,IAAIrC,WAAWiB,OAAO,CAACgB,qBAAqB,GAAIK,GAAG,IAChEN,MAAMK,OAAO,IAAIrC,WAAWiB,OAAO,CAACgB,qBAAqB,GAAIM,MAAM,GACnE,KAAI,GACR;gBACAT;YACF;QACF;QAEA,SAASU,cAAcR,KAAoB;YACzC,IAAIA,MAAM1B,GAAG,KAAK,UAAU;gBAC1BwB;YACF;QACF;QAEAW,SAASvB,gBAAgB,CAAC,aAAaa;QACvCU,SAASvB,gBAAgB,CAAC,WAAWsB;QAErC,OAAO;YACLC,SAASpB,mBAAmB,CAAC,aAAaU;YAC1CU,SAASpB,mBAAmB,CAAC,WAAWmB;QAC1C;IACA,uDAAuD;IACzD,GAAG;QAACvC;KAAO;AACb;AAEA,sFAAsF;AAEtF,OAAO,MAAMyC,mBAAmB,IAAG;AACnC,OAAO,MAAMC,aAAa,wCAAuC"}