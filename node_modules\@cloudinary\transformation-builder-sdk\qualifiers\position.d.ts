import { PositionQualifier } from "./position/PositionQualifier.js";
/**
 * @description
 * Defines the position of a layer: overlay or underlay.</br>
 *
 * <b>Learn more:</b> {@link https://cloudinary.com/documentation/layers|Applying overlays to images} | {@link https://cloudinary.com/documentation/video_layers|Applying overlays to videos}
 * @namespace Position
 * @memberOf Qualifiers
 * @see {@link Actions.Overlay| The overlay action}
 * @see {@link Actions.Underlay| The underlay action}
 *
 */
export { PositionQualifier as Position };
