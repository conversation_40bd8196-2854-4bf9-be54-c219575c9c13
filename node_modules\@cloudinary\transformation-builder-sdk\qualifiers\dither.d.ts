/**
 * @description Qualifiers for applying an ordered dither filter to the image.
 * @namespace Dither
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Effect.dither|Dither Effect} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function threshold1x1Nondither(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function checkerboard2x1Dither(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function ordered2x2Dispersed(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function ordered3x3Dispersed(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function ordered4x4Dispersed(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function ordered8x8Dispersed(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone4x4Angled(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone6x6Angled(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone8x8Angled(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone4x4Orthogonal(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone6x6Orthogonal(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone8x8Orthogonal(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function halftone16x16Orthogonal(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function circles5x5Black(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function circles5x5White(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function circles6x6Black(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function circles6x6White(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function circles7x7Black(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Dither
 */
declare function circles7x7White(): number;
declare const Dither: {
    checkerboard2x1Dither: typeof checkerboard2x1Dither;
    circles5x5Black: typeof circles5x5Black;
    circles5x5White: typeof circles5x5White;
    circles6x6Black: typeof circles6x6Black;
    circles6x6White: typeof circles6x6White;
    circles7x7Black: typeof circles7x7Black;
    circles7x7White: typeof circles7x7White;
    halftone4x4Angled: typeof halftone4x4Angled;
    halftone4x4Orthogonal: typeof halftone4x4Orthogonal;
    halftone6x6Angled: typeof halftone6x6Angled;
    halftone6x6Orthogonal: typeof halftone6x6Orthogonal;
    halftone8x8Angled: typeof halftone8x8Angled;
    halftone8x8Orthogonal: typeof halftone8x8Orthogonal;
    halftone16x16Orthogonal: typeof halftone16x16Orthogonal;
    ordered2x2Dispersed: typeof ordered2x2Dispersed;
    ordered3x3Dispersed: typeof ordered3x3Dispersed;
    ordered4x4Dispersed: typeof ordered4x4Dispersed;
    ordered8x8Dispersed: typeof ordered8x8Dispersed;
    threshold1x1Nondither: typeof threshold1x1Nondither;
};
export { Dither, checkerboard2x1Dither, circles5x5Black, circles5x5White, circles6x6Black, circles6x6White, circles7x7Black, circles7x7White, halftone4x4Angled, halftone4x4Orthogonal, halftone6x6Angled, halftone6x6Orthogonal, halftone8x8Angled, halftone8x8Orthogonal, halftone16x16Orthogonal, ordered2x2Dispersed, ordered3x3Dispersed, ordered4x4Dispersed, ordered8x8Dispersed, threshold1x1Nondither };
