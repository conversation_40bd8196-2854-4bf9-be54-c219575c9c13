{"version": 3, "sources": ["../../../src/client/request/search-params.browser.prod.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  return promise\n}\n"], "names": ["makeUntrackedExoticSearchParams", "CachedSearchParams", "WeakMap", "underlyingSearchParams", "cachedSearchParams", "get", "promise", "Promise", "resolve", "set", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;8BALoB;AAGpC,MAAMC,qBAAqB,IAAIC;AAExB,SAASF,gCACdG,sBAAoC;IAEpC,MAAMC,qBAAqBH,mBAAmBI,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAChCF,mBAAmBQ,GAAG,CAACN,wBAAwBG;IAE/CI,OAAOC,IAAI,CAACR,wBAAwBS,OAAO,CAAC,CAACC;QAC3C,IAAIC,iCAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHP,OAAe,CAACO,KAAK,GAAGV,sBAAsB,CAACU,KAAK;QACxD;IACF;IAEA,OAAOP;AACT"}