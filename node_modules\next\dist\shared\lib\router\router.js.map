{"version": 3, "sources": ["../../../../src/shared/lib/router/router.ts"], "sourcesContent": ["// tslint:disable:no-console\nimport type { ComponentType } from 'react'\nimport type { DomainLocale } from '../../../server/config'\nimport type { MittEmitter } from '../mitt'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RouterEvent } from '../../../client/router'\nimport type { StyleSheetTuple } from '../../../client/page-loader'\nimport type { UrlObject } from 'url'\nimport type PageLoader from '../../../client/page-loader'\nimport type { AppContextType, NextPageContext, NEXT_DATA } from '../utils'\nimport { removeTrailingSlash } from './utils/remove-trailing-slash'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { handleClientScriptLoad } from '../../../client/script'\nimport isError, { getProperError } from '../../../lib/is-error'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt from '../mitt'\nimport { getLocationOrigin, getURL, loadGetInitialProps, ST } from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport resolveRewrites from './utils/resolve-rewrites'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\nimport { formatWithValidation } from './utils/format-url'\nimport { detectDomainLocale } from '../../../client/detect-domain-locale'\nimport { parsePath } from './utils/parse-path'\nimport { addLocale } from '../../../client/add-locale'\nimport { removeLocale } from '../../../client/remove-locale'\nimport { removeBasePath } from '../../../client/remove-base-path'\nimport { addBasePath } from '../../../client/add-base-path'\nimport { hasBasePath } from '../../../client/has-base-path'\nimport { resolveHref } from '../../../client/resolve-href'\nimport { isAPIRoute } from '../../../lib/is-api-route'\nimport { getNextPathnameInfo } from './utils/get-next-pathname-info'\nimport { formatNextPathnameInfo } from './utils/format-next-pathname-info'\nimport { compareRouterStates } from './utils/compare-states'\nimport { isLocalURL } from './utils/is-local-url'\nimport { isBot } from './utils/is-bot'\nimport { omit } from './utils/omit'\nimport { interpolateAs } from './utils/interpolate-as'\nimport { handleSmoothScroll } from './utils/handle-smooth-scroll'\nimport type { Params } from '../../../server/request/params'\nimport { MATCHED_PATH_HEADER } from '../../../lib/constants'\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n  unstable_skipClientCache?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\nexport type HistoryState =\n  | null\n  | { __NA: true; __N?: false }\n  | { __N: false; __NA?: false }\n  | ({ __NA?: false; __N: true; key: string } & NextHistoryState)\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\ninterface MiddlewareEffectParams<T extends FetchDataOutput> {\n  fetchData?: () => Promise<T>\n  locale?: string\n  asPath: string\n  router: Router\n}\n\nexport async function matchesMiddleware<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<boolean> {\n  const matchers = await Promise.resolve(\n    options.router.pageLoader.getMiddleware()\n  )\n  if (!matchers) return false\n\n  const { pathname: asPathname } = parsePath(options.asPath)\n  // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n  const cleanedAs = hasBasePath(asPathname)\n    ? removeBasePath(asPathname)\n    : asPathname\n  const asWithBasePathAndLocale = addBasePath(\n    addLocale(cleanedAs, options.locale)\n  )\n\n  // Check only path match on client. Matching \"has\" should be done on server\n  // where we can access more info such as headers, HttpOnly cookie, etc.\n  return matchers.some((m) =>\n    new RegExp(m.regexp).test(asWithBasePathAndLocale)\n  )\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router, url, true)\n  const origin = getLocationOrigin()\n  const hrefWasAbsolute = resolvedHref.startsWith(origin)\n  const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefWasAbsolute ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asWasAbsolute ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removeTrailingSlash(denormalizePagePath(pathname))\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removeTrailingSlash(pathname)\n}\n\nfunction getMiddlewareData<T extends FetchDataOutput>(\n  source: string,\n  response: Response,\n  options: MiddlewareEffectParams<T>\n) {\n  const nextConfig = {\n    basePath: options.router.basePath,\n    i18n: { locales: options.router.locales },\n    trailingSlash: Boolean(process.env.__NEXT_TRAILING_SLASH),\n  }\n  const rewriteHeader = response.headers.get('x-nextjs-rewrite')\n\n  let rewriteTarget =\n    rewriteHeader || response.headers.get('x-nextjs-matched-path')\n\n  const matchedPath = response.headers.get(MATCHED_PATH_HEADER)\n\n  if (\n    matchedPath &&\n    !rewriteTarget &&\n    !matchedPath.includes('__next_data_catchall') &&\n    !matchedPath.includes('/_error') &&\n    !matchedPath.includes('/404')\n  ) {\n    // leverage x-matched-path to detect next.config.js rewrites\n    rewriteTarget = matchedPath\n  }\n\n  if (rewriteTarget) {\n    if (\n      rewriteTarget.startsWith('/') ||\n      process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE\n    ) {\n      const parsedRewriteTarget = parseRelativeUrl(rewriteTarget)\n      const pathnameInfo = getNextPathnameInfo(parsedRewriteTarget.pathname, {\n        nextConfig,\n        parseData: true,\n      })\n\n      let fsPathname = removeTrailingSlash(pathnameInfo.pathname)\n      return Promise.all([\n        options.router.pageLoader.getPageList(),\n        getClientBuildManifest(),\n      ]).then(([pages, { __rewrites: rewrites }]: any) => {\n        let as = addLocale(pathnameInfo.pathname, pathnameInfo.locale)\n\n        if (\n          isDynamicRoute(as) ||\n          (!rewriteHeader &&\n            pages.includes(\n              normalizeLocalePath(removeBasePath(as), options.router.locales)\n                .pathname\n            ))\n        ) {\n          const parsedSource = getNextPathnameInfo(\n            parseRelativeUrl(source).pathname,\n            {\n              nextConfig: process.env.__NEXT_HAS_REWRITES\n                ? undefined\n                : nextConfig,\n              parseData: true,\n            }\n          )\n\n          as = addBasePath(parsedSource.pathname)\n          parsedRewriteTarget.pathname = as\n        }\n\n        if (process.env.__NEXT_HAS_REWRITES) {\n          const result = resolveRewrites(\n            as,\n            pages,\n            rewrites,\n            parsedRewriteTarget.query,\n            (path: string) => resolveDynamicRoute(path, pages),\n            options.router.locales\n          )\n\n          if (result.matchedPage) {\n            parsedRewriteTarget.pathname = result.parsedAs.pathname\n            as = parsedRewriteTarget.pathname\n            Object.assign(parsedRewriteTarget.query, result.parsedAs.query)\n          }\n        } else if (!pages.includes(fsPathname)) {\n          const resolvedPathname = resolveDynamicRoute(fsPathname, pages)\n\n          if (resolvedPathname !== fsPathname) {\n            fsPathname = resolvedPathname\n          }\n        }\n\n        const resolvedHref = !pages.includes(fsPathname)\n          ? resolveDynamicRoute(\n              normalizeLocalePath(\n                removeBasePath(parsedRewriteTarget.pathname),\n                options.router.locales\n              ).pathname,\n              pages\n            )\n          : fsPathname\n\n        if (isDynamicRoute(resolvedHref)) {\n          const matches = getRouteMatcher(getRouteRegex(resolvedHref))(as)\n          Object.assign(parsedRewriteTarget.query, matches || {})\n        }\n\n        return {\n          type: 'rewrite' as const,\n          parsedAs: parsedRewriteTarget,\n          resolvedHref,\n        }\n      })\n    }\n    const src = parsePath(source)\n    const pathname = formatNextPathnameInfo({\n      ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n      defaultLocale: options.router.defaultLocale,\n      buildId: '',\n    })\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: `${pathname}${src.query}${src.hash}`,\n    })\n  }\n\n  const redirectTarget = response.headers.get('x-nextjs-redirect')\n\n  if (redirectTarget) {\n    if (redirectTarget.startsWith('/')) {\n      const src = parsePath(redirectTarget)\n      const pathname = formatNextPathnameInfo({\n        ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n        defaultLocale: options.router.defaultLocale,\n        buildId: '',\n      })\n\n      return Promise.resolve({\n        type: 'redirect-internal' as const,\n        newAs: `${pathname}${src.query}${src.hash}`,\n        newUrl: `${pathname}${src.query}${src.hash}`,\n      })\n    }\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: redirectTarget,\n    })\n  }\n\n  return Promise.resolve({ type: 'next' as const })\n}\n\ninterface WithMiddlewareEffectsOutput extends FetchDataOutput {\n  effect: Awaited<ReturnType<typeof getMiddlewareData>>\n}\n\nasync function withMiddlewareEffects<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<WithMiddlewareEffectsOutput | null> {\n  const matches = await matchesMiddleware(options)\n  if (!matches || !options.fetchData) {\n    return null\n  }\n\n  const data = await options.fetchData()\n\n  const effect = await getMiddlewareData(data.dataHref, data.response, options)\n\n  return {\n    dataHref: data.dataHref,\n    json: data.json,\n    response: data.response,\n    text: data.text,\n    cacheKey: data.cacheKey,\n    effect,\n  }\n}\n\nexport type Url = UrlObject | string\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'forward'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n  unstable_skipClientCache?: boolean\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n  route?: string\n  resolvedAs?: string\n  query?: ParsedUrlQuery\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(\n  url: string,\n  attempts: number,\n  options: Pick<RequestInit, 'method' | 'headers'>\n): Promise<Response> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n    method: options.method || 'GET',\n    headers: Object.assign({}, options.headers, {\n      'x-nextjs-data': '1',\n    }),\n  }).then((response) => {\n    return !response.ok && attempts > 1 && response.status >= 500\n      ? fetchRetry(url, attempts - 1, options)\n      : response\n  })\n}\n\ninterface FetchDataOutput {\n  dataHref: string\n  json: Record<string, any> | null\n  response: Response\n  text: string\n  cacheKey: string\n}\n\ninterface FetchNextDataParams {\n  dataHref: string\n  isServerRender: boolean\n  parseJSON: boolean | undefined\n  hasMiddleware?: boolean\n  inflightCache: NextDataCache\n  persistCache: boolean\n  isPrefetch: boolean\n  isBackground?: boolean\n  unstable_skipClientCache?: boolean\n}\n\nfunction tryToParseAsJSON(text: string) {\n  try {\n    return JSON.parse(text)\n  } catch (error) {\n    return null\n  }\n}\n\nfunction fetchNextData({\n  dataHref,\n  inflightCache,\n  isPrefetch,\n  hasMiddleware,\n  isServerRender,\n  parseJSON,\n  persistCache,\n  isBackground,\n  unstable_skipClientCache,\n}: FetchNextDataParams): Promise<FetchDataOutput> {\n  const { href: cacheKey } = new URL(dataHref, window.location.href)\n  const getData = (params?: { method?: 'HEAD' | 'GET' }) =>\n    fetchRetry(dataHref, isServerRender ? 3 : 1, {\n      headers: Object.assign(\n        {} as HeadersInit,\n        isPrefetch ? { purpose: 'prefetch' } : {},\n        isPrefetch && hasMiddleware ? { 'x-middleware-prefetch': '1' } : {},\n        process.env.NEXT_DEPLOYMENT_ID\n          ? { 'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID }\n          : {}\n      ),\n      method: params?.method ?? 'GET',\n    })\n      .then((response) => {\n        if (response.ok && params?.method === 'HEAD') {\n          return { dataHref, response, text: '', json: {}, cacheKey }\n        }\n\n        return response.text().then((text) => {\n          if (!response.ok) {\n            /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */\n            if (\n              hasMiddleware &&\n              [301, 302, 307, 308].includes(response.status)\n            ) {\n              return { dataHref, response, text, json: {}, cacheKey }\n            }\n\n            if (response.status === 404) {\n              if (tryToParseAsJSON(text)?.notFound) {\n                return {\n                  dataHref,\n                  json: { notFound: SSG_DATA_NOT_FOUND },\n                  response,\n                  text,\n                  cacheKey,\n                }\n              }\n            }\n\n            const error = new Error(`Failed to load static props`)\n\n            /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */\n            if (!isServerRender) {\n              markAssetError(error)\n            }\n\n            throw error\n          }\n\n          return {\n            dataHref,\n            json: parseJSON ? tryToParseAsJSON(text) : null,\n            response,\n            text,\n            cacheKey,\n          }\n        })\n      })\n      .then((data) => {\n        if (\n          !persistCache ||\n          process.env.NODE_ENV !== 'production' ||\n          data.response.headers.get('x-middleware-cache') === 'no-cache'\n        ) {\n          delete inflightCache[cacheKey]\n        }\n        return data\n      })\n      .catch((err) => {\n        if (!unstable_skipClientCache) {\n          delete inflightCache[cacheKey]\n        }\n        if (\n          // chrome\n          err.message === 'Failed to fetch' ||\n          // firefox\n          err.message === 'NetworkError when attempting to fetch resource.' ||\n          // safari\n          err.message === 'Load failed'\n        ) {\n          markAssetError(err)\n        }\n        throw err\n      })\n\n  // when skipping client cache we wait to update\n  // inflight cache until successful data response\n  // this allows racing click event with fetching newer data\n  // without blocking navigation when stale data is available\n  if (unstable_skipClientCache && persistCache) {\n    return getData({}).then((data) => {\n      if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n        // only update cache if not marked as no-cache\n        inflightCache[cacheKey] = Promise.resolve(data)\n      }\n\n      return data\n    })\n  }\n\n  if (inflightCache[cacheKey] !== undefined) {\n    return inflightCache[cacheKey]\n  }\n  return (inflightCache[cacheKey] = getData(\n    isBackground ? { method: 'HEAD' } : {}\n  ))\n}\n\ninterface NextDataCache {\n  [asPath: string]: Promise<FetchDataOutput>\n}\n\nexport function createKey() {\n  return Math.random().toString(36).slice(2, 10)\n}\n\nfunction handleHardNavigation({\n  url,\n  router,\n}: {\n  url: string\n  router: Router\n}) {\n  // ensure we don't trigger a hard navigation to the same\n  // URL as this can end up with an infinite refresh\n  if (url === addBasePath(addLocale(router.asPath, router.locale))) {\n    throw new Error(\n      `Invariant: attempted to hard navigate to the same URL ${url} ${location.href}`\n    )\n  }\n  window.location.href = url\n}\n\nconst getCancelledHandler = ({\n  route,\n  router,\n}: {\n  route: string\n  router: Router\n}) => {\n  let cancelled = false\n  const cancel = (router.clc = () => {\n    cancelled = true\n  })\n\n  const handleCancelled = () => {\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === router.clc) {\n      router.clc = null\n    }\n  }\n  return handleCancelled\n}\n\nexport default class Router implements BaseRouter {\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Server Data Cache (full data requests)\n  sdc: NextDataCache = {}\n  // Server Background Cache (HEAD requests)\n  sbc: NextDataCache = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: PageLoader\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter<RouterEvent>\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  _inFlightRoute?: string | undefined\n  _shallow?: boolean | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isReady: boolean\n  isLocaleDomain: boolean\n  isFirstPopStateEvent = true\n  _initialMatchesMiddlewarePromise: Promise<boolean>\n  // static entries filter\n  _bfl_s?: import('../../lib/bloom-filter').BloomFilter\n  // dynamic entires filter\n  _bfl_d?: import('../../lib/bloom-filter').BloomFilter\n\n  private state: Readonly<{\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    asPath: string\n    locale: string | undefined\n    isFallback: boolean\n    isPreview: boolean\n  }>\n\n  private _key: string = createKey()\n\n  static events: MittEmitter<RouterEvent> = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: readonly string[]\n      defaultLocale?: string\n      domainLocales?: readonly DomainLocale[]\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    const route = removeTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n    this.isLocaleDomain = false\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      self.__NEXT_DATA__.isExperimentalCompile ||\n      (self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    this.state = {\n      route,\n      pathname,\n      query,\n      asPath: autoExportDynamic ? pathname : as,\n      isPreview: !!isPreview,\n      locale: process.env.__NEXT_I18N_SUPPORT ? locale : undefined,\n      isFallback,\n    }\n\n    this._initialMatchesMiddlewarePromise = Promise.resolve(false)\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (!as.startsWith('//')) {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options: TransitionOptions = { locale }\n        const asPath = getURL()\n\n        this._initialMatchesMiddlewarePromise = matchesMiddleware({\n          router: this,\n          locale,\n          asPath,\n        }).then((matches) => {\n          // if middleware matches we leave resolving to the change function\n          // as the server needs to resolve for correct priority\n          ;(options as any)._shouldResolveHref = as !== pathname\n\n          this.changeState(\n            'replaceState',\n            matches\n              ? asPath\n              : formatWithValidation({\n                  pathname: addBasePath(pathname),\n                  query,\n                }),\n            asPath,\n            options\n          )\n          return matches\n        })\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const { isFirstPopStateEvent } = this\n    this.isFirstPopStateEvent = false\n\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    // __NA is used to identify if the history entry can be handled by the app-router.\n    if (state.__NA) {\n      window.location.reload()\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    // Safari fires popstateevent when reopening the browser.\n    if (\n      isFirstPopStateEvent &&\n      this.locale === state.options.locale &&\n      state.as === this.asPath\n    ) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, key } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._key !== key) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._key,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + key)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._key = key\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (\n      this.isSsr &&\n      as === addBasePath(this.asPath) &&\n      pathname === addBasePath(this.pathname)\n    ) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n        // @ts-ignore internal value not exposed on types\n        _h: 0,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Go forward in history\n   */\n  forward() {\n    window.history.forward()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._key,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  async _bfl(\n    as: string,\n    resolvedAs?: string,\n    locale?: string | false,\n    skipNavigate?: boolean\n  ) {\n    if (process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED) {\n      if (!this._bfl_s && !this._bfl_d) {\n        const { BloomFilter } =\n          require('../../lib/bloom-filter') as typeof import('../../lib/bloom-filter')\n\n        type Filter = ReturnType<\n          import('../../lib/bloom-filter').BloomFilter['export']\n        >\n        let staticFilterData: Filter | undefined\n        let dynamicFilterData: Filter | undefined\n\n        try {\n          ;({\n            __routerFilterStatic: staticFilterData,\n            __routerFilterDynamic: dynamicFilterData,\n          } = (await getClientBuildManifest()) as any as {\n            __routerFilterStatic?: Filter\n            __routerFilterDynamic?: Filter\n          })\n        } catch (err) {\n          // failed to load build manifest hard navigate\n          // to be safe\n          console.error(err)\n          if (skipNavigate) {\n            return true\n          }\n          handleHardNavigation({\n            url: addBasePath(\n              addLocale(as, locale || this.locale, this.defaultLocale)\n            ),\n            router: this,\n          })\n          return new Promise(() => {})\n        }\n\n        const routerFilterSValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_S_FILTER as any\n\n        if (!staticFilterData && routerFilterSValue) {\n          staticFilterData = routerFilterSValue ? routerFilterSValue : undefined\n        }\n\n        const routerFilterDValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_D_FILTER as any\n\n        if (!dynamicFilterData && routerFilterDValue) {\n          dynamicFilterData = routerFilterDValue\n            ? routerFilterDValue\n            : undefined\n        }\n\n        if (staticFilterData?.numHashes) {\n          this._bfl_s = new BloomFilter(\n            staticFilterData.numItems,\n            staticFilterData.errorRate\n          )\n          this._bfl_s.import(staticFilterData)\n        }\n\n        if (dynamicFilterData?.numHashes) {\n          this._bfl_d = new BloomFilter(\n            dynamicFilterData.numItems,\n            dynamicFilterData.errorRate\n          )\n          this._bfl_d.import(dynamicFilterData)\n        }\n      }\n\n      let matchesBflStatic = false\n      let matchesBflDynamic = false\n      const pathsToCheck: Array<{ as?: string; allowMatchCurrent?: boolean }> =\n        [{ as }, { as: resolvedAs }]\n\n      for (const { as: curAs, allowMatchCurrent } of pathsToCheck) {\n        if (curAs) {\n          const asNoSlash = removeTrailingSlash(\n            new URL(curAs, 'http://n').pathname\n          )\n          const asNoSlashLocale = addBasePath(\n            addLocale(asNoSlash, locale || this.locale)\n          )\n\n          if (\n            allowMatchCurrent ||\n            asNoSlash !==\n              removeTrailingSlash(new URL(this.asPath, 'http://n').pathname)\n          ) {\n            matchesBflStatic =\n              matchesBflStatic ||\n              !!this._bfl_s?.contains(asNoSlash) ||\n              !!this._bfl_s?.contains(asNoSlashLocale)\n\n            for (const normalizedAS of [asNoSlash, asNoSlashLocale]) {\n              // if any sub-path of as matches a dynamic filter path\n              // it should be hard navigated\n              const curAsParts = normalizedAS.split('/')\n              for (\n                let i = 0;\n                !matchesBflDynamic && i < curAsParts.length + 1;\n                i++\n              ) {\n                const currentPart = curAsParts.slice(0, i).join('/')\n                if (currentPart && this._bfl_d?.contains(currentPart)) {\n                  matchesBflDynamic = true\n                  break\n                }\n              }\n            }\n\n            // if the client router filter is matched then we trigger\n            // a hard navigation\n            if (matchesBflStatic || matchesBflDynamic) {\n              if (skipNavigate) {\n                return true\n              }\n              handleHardNavigation({\n                url: addBasePath(\n                  addLocale(as, locale || this.locale, this.defaultLocale)\n                ),\n                router: this,\n              })\n              return new Promise(() => {})\n            }\n          }\n        }\n      }\n    }\n    return false\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      handleHardNavigation({ url, router: this })\n      return false\n    }\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    const isQueryUpdating = (options as any)._h === 1\n\n    if (!isQueryUpdating && !options.shallow) {\n      await this._bfl(as, undefined, options.locale)\n    }\n\n    let shouldResolveHref =\n      isQueryUpdating ||\n      (options as any)._shouldResolveHref ||\n      parsePath(url).pathname === parsePath(as).pathname\n\n    const nextState = {\n      ...this.state,\n    }\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    // or a navigation has occurred\n    const readyStateChange = this.isReady !== true\n    this.isReady = true\n    const isSsr = this.isSsr\n\n    if (!isQueryUpdating) {\n      this.isSsr = false\n    }\n\n    // if a route transition is already in progress before\n    // the query updating is triggered ignore query updating\n    if (isQueryUpdating && this.clc) {\n      return false\n    }\n\n    const prevLocale = nextState.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      nextState.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || nextState.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = nextState.locale\n      }\n\n      const parsedAs = parseRelativeUrl(\n        hasBasePath(as) ? removeBasePath(as) : as\n      )\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        nextState.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? removeBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(nextState.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, nextState.locale)\n          handleHardNavigation({\n            url: formatWithValidation(parsedAs),\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        nextState.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = removeBasePath(as)\n          handleHardNavigation({\n            url: `http${detectedDomain.http ? '' : 's'}://${\n              detectedDomain.domain\n            }${addBasePath(\n              `${\n                nextState.locale === detectedDomain.defaultLocale\n                  ? ''\n                  : `/${nextState.locale}`\n              }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n            )}`,\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false, scroll = true } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute && this.clc) {\n      if (!isSsr) {\n        Router.events.emit(\n          'routeChangeError',\n          buildCancellationError(),\n          this._inFlightRoute,\n          routeProps\n        )\n      }\n      this.clc()\n      this.clc = null\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? removeBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = removeLocale(\n      hasBasePath(as) ? removeBasePath(as) : as,\n      nextState.locale\n    )\n    this._inFlightRoute = as\n\n    const localeChange = prevLocale !== nextState.locale\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n      nextState.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, {\n        ...options,\n        scroll: false,\n      })\n      if (scroll) {\n        this.scrollToHash(cleanedAs)\n      }\n      try {\n        await this.set(nextState, this.components[nextState.route], null)\n      } catch (err) {\n        if (isError(err) && err.cancelled) {\n          Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n        }\n        throw err\n      }\n\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: string[], rewrites: any\n    try {\n      ;[pages, { __rewrites: rewrites }] = await Promise.all([\n        this.pageLoader.getPageList(),\n        getClientBuildManifest(),\n        this.pageLoader.getMiddleware(),\n      ])\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removeTrailingSlash(removeBasePath(pathname))\n      : pathname\n\n    let route = removeTrailingSlash(pathname)\n    const parsedAsPathname = as.startsWith('/') && parseRelativeUrl(as).pathname\n\n    // if we detected the path as app route during prefetching\n    // trigger hard navigation\n    if ((this.components[pathname] as any)?.__appRouter) {\n      handleHardNavigation({ url: as, router: this })\n      return new Promise(() => {})\n    }\n\n    const isMiddlewareRewrite = !!(\n      parsedAsPathname &&\n      route !== parsedAsPathname &&\n      (!isDynamicRoute(route) ||\n        !getRouteMatcher(getRouteRegex(route))(parsedAsPathname))\n    )\n\n    // we don't attempt resolve asPath when we need to execute\n    // middleware as the resolving will occur server-side\n    const isMiddlewareMatch =\n      !options.shallow &&\n      (await matchesMiddleware({\n        asPath: as,\n        locale: nextState.locale,\n        router: this,\n      }))\n\n    if (isQueryUpdating && isMiddlewareMatch) {\n      shouldResolveHref = false\n    }\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;(options as any)._shouldResolveHref = true\n\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, nextState.locale), true),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n\n        if (rewritesResult.externalDest) {\n          handleHardNavigation({ url: as, router: this })\n          return true\n        }\n        if (!isMiddlewareMatch) {\n          resolvedAs = rewritesResult.asPath\n        }\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      }\n    }\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    resolvedAs = removeLocale(removeBasePath(resolvedAs), nextState.locale)\n\n    route = removeTrailingSlash(pathname)\n    let routeMatch: Params | false = false\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param] && !routeRegex.groups[param].optional\n        )\n\n        if (missingParams.length > 0 && !isMiddlewareMatch) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omit(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    if (!isQueryUpdating) {\n      Router.events.emit('routeChangeStart', as, routeProps)\n    }\n\n    const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error'\n\n    try {\n      let routeInfo = await this.getRouteInfo({\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps,\n        locale: nextState.locale,\n        isPreview: nextState.isPreview,\n        hasMiddleware: isMiddlewareMatch,\n        unstable_skipClientCache: options.unstable_skipClientCache,\n        isQueryUpdating: isQueryUpdating && !this.isFallback,\n        isMiddlewareRewrite,\n      })\n\n      if (!isQueryUpdating && !options.shallow) {\n        await this._bfl(\n          as,\n          'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined,\n          nextState.locale\n        )\n      }\n\n      if ('route' in routeInfo && isMiddlewareMatch) {\n        pathname = routeInfo.route || route\n        route = pathname\n\n        if (!routeProps.shallow) {\n          query = Object.assign({}, routeInfo.query || {}, query)\n        }\n\n        const cleanedParsedPathname = hasBasePath(parsed.pathname)\n          ? removeBasePath(parsed.pathname)\n          : parsed.pathname\n\n        if (routeMatch && pathname !== cleanedParsedPathname) {\n          Object.keys(routeMatch).forEach((key) => {\n            if (routeMatch && query[key] === routeMatch[key]) {\n              delete query[key]\n            }\n          })\n        }\n\n        if (isDynamicRoute(pathname)) {\n          const prefixedAs =\n            !routeProps.shallow && routeInfo.resolvedAs\n              ? routeInfo.resolvedAs\n              : addBasePath(\n                  addLocale(\n                    new URL(as, location.href).pathname,\n                    nextState.locale\n                  ),\n                  true\n                )\n\n          let rewriteAs = prefixedAs\n\n          if (hasBasePath(rewriteAs)) {\n            rewriteAs = removeBasePath(rewriteAs)\n          }\n\n          if (process.env.__NEXT_I18N_SUPPORT) {\n            const localeResult = normalizeLocalePath(rewriteAs, this.locales)\n            nextState.locale = localeResult.detectedLocale || nextState.locale\n            rewriteAs = localeResult.pathname\n          }\n          const routeRegex = getRouteRegex(pathname)\n          const curRouteMatch = getRouteMatcher(routeRegex)(\n            new URL(rewriteAs, location.href).pathname\n          )\n\n          if (curRouteMatch) {\n            Object.assign(query, curRouteMatch)\n          }\n        }\n      }\n\n      // If the routeInfo brings a redirect we simply apply it.\n      if ('type' in routeInfo) {\n        if (routeInfo.type === 'redirect-internal') {\n          return this.change(method, routeInfo.newUrl, routeInfo.newAs, options)\n        } else {\n          handleHardNavigation({ url: routeInfo.destination, router: this })\n          return new Promise(() => {})\n        }\n      }\n\n      const component: any = routeInfo.Component\n      if (component && component.unstable_scriptLoader) {\n        const scripts = [].concat(component.unstable_scriptLoader())\n\n        scripts.forEach((script: any) => {\n          handleClientScriptLoad(script.props)\n        })\n      }\n\n      // handle redirect on client-transition\n      if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n        if (\n          routeInfo.props.pageProps &&\n          routeInfo.props.pageProps.__N_REDIRECT\n        ) {\n          // Use the destination from redirect without adding locale\n          options.locale = false\n\n          const destination = routeInfo.props.pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (\n            destination.startsWith('/') &&\n            routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false\n          ) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            const { url: newUrl, as: newAs } = prepareUrlAs(\n              this,\n              destination,\n              destination\n            )\n            return this.change(method, newUrl, newAs, options)\n          }\n          handleHardNavigation({ url: destination, router: this })\n          return new Promise(() => {})\n        }\n\n        nextState.isPreview = !!routeInfo.props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo({\n            route: notFoundRoute,\n            pathname: notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            routeProps: { shallow: false },\n            locale: nextState.locale,\n            isPreview: nextState.isPreview,\n            isNotFound: true,\n          })\n\n          if ('type' in routeInfo) {\n            throw new Error(`Unexpected middleware effect on /404`)\n          }\n        }\n      }\n\n      if (\n        isQueryUpdating &&\n        this.pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        routeInfo.props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        routeInfo.props.pageProps.statusCode = 500\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute =\n        options.shallow && nextState.route === (routeInfo.route ?? route)\n\n      const shouldScroll =\n        options.scroll ?? (!isQueryUpdating && !isValidShallowRoute)\n      const resetScroll = shouldScroll ? { x: 0, y: 0 } : null\n      const upcomingScrollState = forcedScroll ?? resetScroll\n\n      // the new state that the router gonna set\n      const upcomingRouterState = {\n        ...nextState,\n        route,\n        pathname,\n        query,\n        asPath: cleanedAs,\n        isFallback: false,\n      }\n\n      // When the page being rendered is the 404 page, we should only update the\n      // query parameters. Route changes here might add the basePath when it\n      // wasn't originally present. This is also why this block is before the\n      // below `changeState` call which updates the browser's history (changing\n      // the URL).\n      if (isQueryUpdating && isErrorRoute) {\n        routeInfo = await this.getRouteInfo({\n          route: this.pathname,\n          pathname: this.pathname,\n          query,\n          as,\n          resolvedAs,\n          routeProps: { shallow: false },\n          locale: nextState.locale,\n          isPreview: nextState.isPreview,\n          isQueryUpdating: isQueryUpdating && !this.isFallback,\n        })\n\n        if ('type' in routeInfo) {\n          throw new Error(`Unexpected middleware effect on ${this.pathname}`)\n        }\n\n        if (\n          this.pathname === '/_error' &&\n          self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n          routeInfo.props?.pageProps\n        ) {\n          // ensure statusCode is still correct for static 500 page\n          // when updating query information\n          routeInfo.props.pageProps.statusCode = 500\n        }\n\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (err) {\n          if (isError(err) && err.cancelled) {\n            Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n          }\n          throw err\n        }\n\n        return true\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      // for query updates we can skip it if the state is unchanged and we don't\n      // need to scroll\n      // https://github.com/vercel/next.js/issues/37139\n      const canSkipUpdating =\n        isQueryUpdating &&\n        !upcomingScrollState &&\n        !readyStateChange &&\n        !localeChange &&\n        compareRouterStates(upcomingRouterState, this.state)\n\n      if (!canSkipUpdating) {\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (e: any) {\n          if (e.cancelled) routeInfo.error = routeInfo.error || e\n          else throw e\n        }\n\n        if (routeInfo.error) {\n          if (!isQueryUpdating) {\n            Router.events.emit(\n              'routeChangeError',\n              routeInfo.error,\n              cleanedAs,\n              routeProps\n            )\n          }\n\n          throw routeInfo.error\n        }\n\n        if (process.env.__NEXT_I18N_SUPPORT) {\n          if (nextState.locale) {\n            document.documentElement.lang = nextState.locale\n          }\n        }\n\n        if (!isQueryUpdating) {\n          Router.events.emit('routeChangeComplete', as, routeProps)\n        }\n\n        // A hash mark # is the optional last part of a URL\n        const hashRegex = /#.+$/\n        if (shouldScroll && hashRegex.test(as)) {\n          this.scrollToHash(as)\n        }\n      }\n\n      return true\n    } catch (err) {\n      if (isError(err) && err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          key: (this._key = method !== 'pushState' ? this._key : createKey()),\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code?: any; cancelled?: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      handleHardNavigation({\n        url: as,\n        router: this,\n      })\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    console.error(err)\n\n    try {\n      let props: Record<string, any> | undefined\n      const { page: Component, styleSheets } =\n        await this.fetchComponent('/_error')\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        isError(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + ''),\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo({\n    route: requestedRoute,\n    pathname,\n    query,\n    as,\n    resolvedAs,\n    routeProps,\n    locale,\n    hasMiddleware,\n    isPreview,\n    unstable_skipClientCache,\n    isQueryUpdating,\n    isMiddlewareRewrite,\n    isNotFound,\n  }: {\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    as: string\n    resolvedAs: string\n    hasMiddleware?: boolean\n    routeProps: RouteProperties\n    locale: string | undefined\n    isPreview: boolean\n    unstable_skipClientCache?: boolean\n    isQueryUpdating?: boolean\n    isMiddlewareRewrite?: boolean\n    isNotFound?: boolean\n  }) {\n    /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */\n    let route = requestedRoute\n\n    try {\n      let existingInfo: PrivateRouteInfo | undefined = this.components[route]\n      if (routeProps.shallow && existingInfo && this.route === route) {\n        return existingInfo\n      }\n\n      const handleCancelled = getCancelledHandler({ route, router: this })\n\n      if (hasMiddleware) {\n        existingInfo = undefined\n      }\n\n      let cachedRouteInfo =\n        existingInfo &&\n        !('initial' in existingInfo) &&\n        process.env.NODE_ENV !== 'development'\n          ? existingInfo\n          : undefined\n\n      const isBackground = isQueryUpdating\n      const fetchNextDataParams: FetchNextDataParams = {\n        dataHref: this.pageLoader.getDataHref({\n          href: formatWithValidation({ pathname, query }),\n          skipInterpolation: true,\n          asPath: isNotFound ? '/404' : resolvedAs,\n          locale,\n        }),\n        hasMiddleware: true,\n        isServerRender: this.isSsr,\n        parseJSON: true,\n        inflightCache: isBackground ? this.sbc : this.sdc,\n        persistCache: !isPreview,\n        isPrefetch: false,\n        unstable_skipClientCache,\n        isBackground,\n      }\n\n      let data:\n        | WithMiddlewareEffectsOutput\n        | (Pick<WithMiddlewareEffectsOutput, 'json'> &\n            Omit<Partial<WithMiddlewareEffectsOutput>, 'json'>)\n        | null =\n        isQueryUpdating && !isMiddlewareRewrite\n          ? null\n          : await withMiddlewareEffects({\n              fetchData: () => fetchNextData(fetchNextDataParams),\n              asPath: isNotFound ? '/404' : resolvedAs,\n              locale: locale,\n              router: this,\n            }).catch((err) => {\n              // we don't hard error during query updating\n              // as it's un-necessary and doesn't need to be fatal\n              // unless it is a fallback route and the props can't\n              // be loaded\n              if (isQueryUpdating) {\n                return null\n              }\n              throw err\n            })\n\n      // when rendering error routes we don't apply middleware\n      // effects\n      if (data && (pathname === '/_error' || pathname === '/404')) {\n        data.effect = undefined\n      }\n\n      if (isQueryUpdating) {\n        if (!data) {\n          data = { json: self.__NEXT_DATA__.props }\n        } else {\n          data.json = self.__NEXT_DATA__.props\n        }\n      }\n\n      handleCancelled()\n\n      if (\n        data?.effect?.type === 'redirect-internal' ||\n        data?.effect?.type === 'redirect-external'\n      ) {\n        return data.effect\n      }\n\n      if (data?.effect?.type === 'rewrite') {\n        const resolvedRoute = removeTrailingSlash(data.effect.resolvedHref)\n        const pages = await this.pageLoader.getPageList()\n\n        // during query updating the page must match although during\n        // client-transition a redirect that doesn't match a page\n        // can be returned and this should trigger a hard navigation\n        // which is valid for incremental migration\n        if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n          route = resolvedRoute\n          pathname = data.effect.resolvedHref\n          query = { ...query, ...data.effect.parsedAs.query }\n          resolvedAs = removeBasePath(\n            normalizeLocalePath(data.effect.parsedAs.pathname, this.locales)\n              .pathname\n          )\n\n          // Check again the cache with the new destination.\n          existingInfo = this.components[route]\n          if (\n            routeProps.shallow &&\n            existingInfo &&\n            this.route === route &&\n            !hasMiddleware\n          ) {\n            // If we have a match with the current route due to rewrite,\n            // we can copy the existing information to the rewritten one.\n            // Then, we return the information along with the matched route.\n            return { ...existingInfo, route }\n          }\n        }\n      }\n\n      if (isAPIRoute(route)) {\n        handleHardNavigation({ url: as, router: this })\n        return new Promise<never>(() => {})\n      }\n\n      const routeInfo =\n        cachedRouteInfo ||\n        (await this.fetchComponent(route).then<CompletePrivateRouteInfo>(\n          (res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          })\n        ))\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } = require('next/dist/compiled/react-is')\n        if (!isValidElementType(routeInfo.Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n      const wasBailedPrefetch = data?.response?.headers.get('x-middleware-skip')\n\n      const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP\n\n      // For non-SSG prefetches that bailed before sending data\n      // we clear the cache to fetch full response\n      if (wasBailedPrefetch && data?.dataHref) {\n        delete this.sdc[data.dataHref]\n      }\n\n      const { props, cacheKey } = await this._getData(async () => {\n        if (shouldFetchData) {\n          if (data?.json && !wasBailedPrefetch) {\n            return { cacheKey: data.cacheKey, props: data.json }\n          }\n\n          const dataHref = data?.dataHref\n            ? data.dataHref\n            : this.pageLoader.getDataHref({\n                href: formatWithValidation({ pathname, query }),\n                asPath: resolvedAs,\n                locale,\n              })\n\n          const fetched = await fetchNextData({\n            dataHref,\n            isServerRender: this.isSsr,\n            parseJSON: true,\n            inflightCache: wasBailedPrefetch ? {} : this.sdc,\n            persistCache: !isPreview,\n            isPrefetch: false,\n            unstable_skipClientCache,\n          })\n\n          return {\n            cacheKey: fetched.cacheKey,\n            props: fetched.json || {},\n          }\n        }\n\n        return {\n          headers: {},\n          props: await this.getInitialProps(\n            routeInfo.Component,\n            // we provide AppTree later so this needs to be `any`\n            {\n              pathname,\n              query,\n              asPath: as,\n              locale,\n              locales: this.locales,\n              defaultLocale: this.defaultLocale,\n            } as any\n          ),\n        }\n      })\n\n      // Only bust the data cache for SSP routes although\n      // middleware can skip cache per request with\n      // x-middleware-cache: no-cache as well\n      if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n        delete this.sdc[cacheKey]\n      }\n\n      // we kick off a HEAD request in the background\n      // when a non-prefetch request is made to signal revalidation\n      if (\n        !this.isPreview &&\n        routeInfo.__N_SSG &&\n        process.env.NODE_ENV !== 'development' &&\n        !isQueryUpdating\n      ) {\n        fetchNextData(\n          Object.assign({}, fetchNextDataParams, {\n            isBackground: true,\n            persistCache: false,\n            inflightCache: this.sbc,\n          })\n        ).catch(() => {})\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps)\n      routeInfo.props = props\n      routeInfo.route = route\n      routeInfo.query = query\n      routeInfo.resolvedAs = resolvedAs\n      this.components[route] = routeInfo\n\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(\n        getProperError(err),\n        pathname,\n        query,\n        as,\n        routeProps\n      )\n    }\n  }\n\n  private set(\n    state: typeof this.state,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.state = state\n\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2)\n    const [newUrlNoHash, newHash] = as.split('#', 2)\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash = ''] = as.split('#', 2)\n\n    handleSmoothScroll(\n      () => {\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === '' || hash === 'top') {\n          window.scrollTo(0, 0)\n          return\n        }\n\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash)\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash)\n        if (idEl) {\n          idEl.scrollIntoView()\n          return\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0]\n        if (nameEl) {\n          nameEl.scrollIntoView()\n        }\n      },\n      {\n        onlyHashChange: this.onlyAHashChange(as),\n      }\n    )\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    if (typeof window !== 'undefined' && isBot(window.navigator.userAgent)) {\n      // No prefetches for bots that render the link since they are typically navigating\n      // links via the equivalent of a hard navigation and hence never utilize these\n      // prefetches.\n      return\n    }\n    let parsed = parseRelativeUrl(url)\n    const urlPathname = parsed.pathname\n\n    let { pathname, query } = parsed\n    const originalPathname = pathname\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    const locale =\n      typeof options.locale !== 'undefined'\n        ? options.locale || undefined\n        : this.locale\n\n    const isMiddlewareMatch = await matchesMiddleware({\n      asPath: asPath,\n      locale: locale,\n      router: this,\n    })\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale), true),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n\n      if (rewritesResult.externalDest) {\n        return\n      }\n\n      if (!isMiddlewareMatch) {\n        resolvedAs = removeLocale(\n          removeBasePath(rewritesResult.asPath),\n          this.locale\n        )\n      }\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n\n        if (!isMiddlewareMatch) {\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n    parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n    if (isDynamicRoute(parsed.pathname)) {\n      pathname = parsed.pathname\n      parsed.pathname = pathname\n      Object.assign(\n        query,\n        getRouteMatcher(getRouteRegex(parsed.pathname))(\n          parsePath(asPath).pathname\n        ) || {}\n      )\n\n      if (!isMiddlewareMatch) {\n        url = formatWithValidation(parsed)\n      }\n    }\n\n    const data =\n      process.env.__NEXT_MIDDLEWARE_PREFETCH === 'strict'\n        ? null\n        : await withMiddlewareEffects({\n            fetchData: () =>\n              fetchNextData({\n                dataHref: this.pageLoader.getDataHref({\n                  href: formatWithValidation({\n                    pathname: originalPathname,\n                    query,\n                  }),\n                  skipInterpolation: true,\n                  asPath: resolvedAs,\n                  locale,\n                }),\n                hasMiddleware: true,\n                isServerRender: false,\n                parseJSON: true,\n                inflightCache: this.sdc,\n                persistCache: !this.isPreview,\n                isPrefetch: true,\n              }),\n            asPath: asPath,\n            locale: locale,\n            router: this,\n          })\n\n    /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */\n    if (data?.effect.type === 'rewrite') {\n      parsed.pathname = data.effect.resolvedHref\n      pathname = data.effect.resolvedHref\n      query = { ...query, ...data.effect.parsedAs.query }\n      resolvedAs = data.effect.parsedAs.pathname\n      url = formatWithValidation(parsed)\n    }\n\n    /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */\n    if (data?.effect.type === 'redirect-external') {\n      return\n    }\n\n    const route = removeTrailingSlash(pathname)\n\n    if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n      this.components[urlPathname] = { __appRouter: true } as any\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg) => {\n        return isSsg\n          ? fetchNextData({\n              dataHref: data?.json\n                ? data?.dataHref\n                : this.pageLoader.getDataHref({\n                    href: url,\n                    asPath: resolvedAs,\n                    locale: locale,\n                  }),\n              isServerRender: false,\n              parseJSON: true,\n              inflightCache: this.sdc,\n              persistCache: !this.isPreview,\n              isPrefetch: true,\n              unstable_skipClientCache:\n                options.unstable_skipClientCache ||\n                (options.priority &&\n                  !!process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE),\n            })\n              .then(() => false)\n              .catch(() => false)\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string) {\n    const handleCancelled = getCancelledHandler({ route, router: this })\n\n    try {\n      const componentResult = await this.pageLoader.loadPage(route)\n      handleCancelled()\n\n      return componentResult\n    } catch (err) {\n      handleCancelled()\n      throw err\n    }\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<Record<string, any>> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  get route(): string {\n    return this.state.route\n  }\n\n  get pathname(): string {\n    return this.state.pathname\n  }\n\n  get query(): ParsedUrlQuery {\n    return this.state.query\n  }\n\n  get asPath(): string {\n    return this.state.asPath\n  }\n\n  get locale(): string | undefined {\n    return this.state.locale\n  }\n\n  get isFallback(): boolean {\n    return this.state.isFallback\n  }\n\n  get isPreview(): boolean {\n    return this.state.isPreview\n  }\n}\n"], "names": ["create<PERSON><PERSON>", "Router", "matchesMiddleware", "buildCancellationError", "Object", "assign", "Error", "cancelled", "options", "matchers", "Promise", "resolve", "router", "page<PERSON><PERSON>der", "getMiddleware", "pathname", "asPathname", "parsePath", "<PERSON><PERSON><PERSON>", "cleanedAs", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "asWithBasePathAndLocale", "addBasePath", "addLocale", "locale", "some", "m", "RegExp", "regexp", "test", "strip<PERSON><PERSON>in", "url", "origin", "getLocationOrigin", "startsWith", "substring", "length", "prepareUrlAs", "as", "resolvedHref", "resolvedAs", "resolveHref", "hrefWasAbsolute", "asWasAbsolute", "preparedUrl", "preparedAs", "resolveDynamicRoute", "pages", "cleanPathname", "removeTrailingSlash", "denormalizePagePath", "includes", "page", "isDynamicRoute", "getRouteRegex", "re", "getMiddlewareData", "source", "response", "nextConfig", "basePath", "i18n", "locales", "trailingSlash", "Boolean", "process", "env", "__NEXT_TRAILING_SLASH", "rewriteHeader", "headers", "get", "rewriteTarget", "<PERSON><PERSON><PERSON>", "MATCHED_PATH_HEADER", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "parsedRewriteTarget", "parseRelativeUrl", "pathnameInfo", "getNextPathnameInfo", "parseData", "fsPathname", "all", "getPageList", "getClientBuildManifest", "then", "__rewrites", "rewrites", "normalizeLocalePath", "parsedSource", "__NEXT_HAS_REWRITES", "undefined", "result", "resolveRewrites", "query", "path", "matchedPage", "parsedAs", "resolvedPathname", "matches", "getRouteMatcher", "type", "src", "formatNextPathnameInfo", "defaultLocale", "buildId", "destination", "hash", "redirectTarget", "newAs", "newUrl", "withMiddlewareEffects", "fetchData", "data", "effect", "dataHref", "json", "text", "cache<PERSON>ey", "manualScrollRestoration", "__NEXT_SCROLL_RESTORATION", "window", "history", "v", "sessionStorage", "setItem", "removeItem", "n", "SSG_DATA_NOT_FOUND", "Symbol", "fetchRetry", "attempts", "fetch", "credentials", "method", "ok", "status", "tryToParseAsJSON", "JSON", "parse", "error", "fetchNextData", "inflightCache", "isPrefetch", "hasMiddleware", "isServerRender", "parseJSON", "persistCache", "isBackground", "unstable_skipClientCache", "href", "URL", "location", "getData", "params", "purpose", "NEXT_DEPLOYMENT_ID", "notFound", "<PERSON><PERSON><PERSON><PERSON>", "NODE_ENV", "catch", "err", "message", "Math", "random", "toString", "slice", "handleHardNavigation", "getCancelledHandler", "route", "cancel", "clc", "handleCancelled", "reload", "back", "forward", "push", "_key", "stringify", "x", "self", "pageXOffset", "y", "pageYOffset", "change", "replace", "_bfl", "skipNavigate", "__NEXT_CLIENT_ROUTER_FILTER_ENABLED", "_bfl_s", "_bfl_d", "<PERSON><PERSON><PERSON><PERSON>", "require", "staticFilterData", "dynamicFilterData", "__routerFilterStatic", "__routerFilterDynamic", "console", "routerFilterSValue", "__NEXT_CLIENT_ROUTER_S_FILTER", "routerFilterDValue", "__NEXT_CLIENT_ROUTER_D_FILTER", "numHashes", "numItems", "errorRate", "import", "matchesBflStatic", "matchesBflDynamic", "pathsToCheck", "curAs", "allowMatchCurrent", "asNoSlash", "asNoSlashLocale", "contains", "normalizedAS", "curAs<PERSON><PERSON>s", "split", "i", "currentPart", "join", "forcedScroll", "isLocalURL", "isQueryUpdating", "_h", "shallow", "shouldResolveHref", "_shouldResolveHref", "nextState", "state", "readyStateChange", "isReady", "isSsr", "prevLocale", "__NEXT_I18N_SUPPORT", "localePathResult", "detectedLocale", "formatWithValidation", "didNavigate", "detectedDomain", "detectDomainLocale", "domainLocales", "isLocaleDomain", "hostname", "domain", "asNoBasePath", "http", "ST", "performance", "mark", "scroll", "routeProps", "_inFlightRoute", "events", "emit", "removeLocale", "localeChange", "onlyAHashChange", "changeState", "scrollToHash", "set", "components", "isError", "parsed", "urlIsNew", "parsedAsPathname", "__appRouter", "isMiddlewareRewrite", "isMiddlewareMatch", "rewritesResult", "p", "externalDest", "routeMatch", "routeRegex", "shouldInterpolate", "interpolatedAs", "interpolateAs", "missingParams", "keys", "groups", "filter", "param", "optional", "warn", "omit", "isErrorRoute", "routeInfo", "getRouteInfo", "isPreview", "<PERSON><PERSON><PERSON><PERSON>", "cleanedParsedPathname", "for<PERSON>ach", "key", "prefixedAs", "rewriteAs", "localeResult", "cur<PERSON><PERSON>eMatch", "component", "Component", "unstable_scriptLoader", "scripts", "concat", "script", "handleClientScriptLoad", "props", "__N_SSG", "__N_SSP", "pageProps", "__N_REDIRECT", "__N_REDIRECT_BASE_PATH", "parsedHref", "__N_PREVIEW", "notFoundRoute", "fetchComponent", "_", "isNotFound", "__NEXT_DATA__", "statusCode", "isValidShallowRoute", "shouldScroll", "resetScroll", "upcomingScrollState", "upcomingRouterState", "canSkipUpdating", "compareRouterStates", "e", "document", "documentElement", "lang", "hashRegex", "getURL", "_shallow", "__N", "handleRouteInfoError", "loadErrorFail", "isAssetError", "styleSheets", "getInitialProps", "gipErr", "routeInfoErr", "requestedRoute", "existingInfo", "cachedRouteInfo", "fetchNextDataParams", "getDataHref", "skipInterpolation", "sbc", "sdc", "resolvedRoute", "isAPIRoute", "res", "mod", "isValidElementType", "wasBailedPrefetch", "shouldFetchData", "_getData", "fetched", "getProperError", "sub", "beforePopState", "cb", "_bps", "oldUrlNoHash", "oldHash", "newUrlNoHash", "newHash", "handleSmoothScroll", "scrollTo", "rawHash", "decodeURIComponent", "idEl", "getElementById", "scrollIntoView", "nameEl", "getElementsByName", "onlyHashChange", "prefetch", "isBot", "navigator", "userAgent", "urlPathname", "originalPathname", "__NEXT_MIDDLEWARE_PREFETCH", "_isSsg", "isSsg", "priority", "__NEXT_OPTIMISTIC_CLIENT_CACHE", "componentResult", "loadPage", "fn", "ctx", "App", "AppTree", "_wrapApp", "loadGetInitialProps", "constructor", "initialProps", "wrapApp", "subscription", "isFirstPopStateEvent", "onPopState", "__NA", "getItem", "initial", "autoExportDynamic", "autoExport", "__NEXT_ROUTER_BASEPATH", "gssp", "gip", "isExperimentalCompile", "appGip", "gsp", "search", "_initialMatchesMiddlewarePromise", "addEventListener", "scrollRestoration", "mitt"], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;;;;;;IAwmBZA,SAAS;eAATA;;;eAiDKC;;IA9jBCC,iBAAiB;eAAjBA;;;;;qCAjFc;6BAK7B;wBACgC;mEACC;qCACJ;qCACA;+DACnB;uBACkD;2BACpC;kCACE;0EACL;8BACI;4BACF;2BACO;oCACF;2BACT;2BACA;8BACG;gCACE;6BACH;6BACA;6BACA;4BACD;qCACS;wCACG;+BACH;4BACT;uBACL;sBACD;+BACS;oCACK;2BAEC;AAgCpC,SAASC;IACP,OAAOC,OAAOC,MAAM,CAAC,qBAA4B,CAA5B,IAAIC,MAAM,oBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2B,IAAG;QACjDC,WAAW;IACb;AACF;AASO,eAAeL,kBACpBM,OAAkC;IAElC,MAAMC,WAAW,MAAMC,QAAQC,OAAO,CACpCH,QAAQI,MAAM,CAACC,UAAU,CAACC,aAAa;IAEzC,IAAI,CAACL,UAAU,OAAO;IAEtB,MAAM,EAAEM,UAAUC,UAAU,EAAE,GAAGC,IAAAA,oBAAS,EAACT,QAAQU,MAAM;IACzD,6FAA6F;IAC7F,MAAMC,YAAYC,IAAAA,wBAAW,EAACJ,cAC1BK,IAAAA,8BAAc,EAACL,cACfA;IACJ,MAAMM,0BAA0BC,IAAAA,wBAAW,EACzCC,IAAAA,oBAAS,EAACL,WAAWX,QAAQiB,MAAM;IAGrC,2EAA2E;IAC3E,uEAAuE;IACvE,OAAOhB,SAASiB,IAAI,CAAC,CAACC,IACpB,IAAIC,OAAOD,EAAEE,MAAM,EAAEC,IAAI,CAACR;AAE9B;AAEA,SAASS,YAAYC,GAAW;IAC9B,MAAMC,SAASC,IAAAA,wBAAiB;IAEhC,OAAOF,IAAIG,UAAU,CAACF,UAAUD,IAAII,SAAS,CAACH,OAAOI,MAAM,IAAIL;AACjE;AAEA,SAASM,aAAa1B,MAAkB,EAAEoB,GAAQ,EAAEO,EAAQ;IAC1D,sDAAsD;IACtD,kDAAkD;IAClD,IAAI,CAACC,cAAcC,WAAW,GAAGC,IAAAA,wBAAW,EAAC9B,QAAQoB,KAAK;IAC1D,MAAMC,SAASC,IAAAA,wBAAiB;IAChC,MAAMS,kBAAkBH,aAAaL,UAAU,CAACF;IAChD,MAAMW,gBAAgBH,cAAcA,WAAWN,UAAU,CAACF;IAE1DO,eAAeT,YAAYS;IAC3BC,aAAaA,aAAaV,YAAYU,cAAcA;IAEpD,MAAMI,cAAcF,kBAAkBH,eAAejB,IAAAA,wBAAW,EAACiB;IACjE,MAAMM,aAAaP,KACfR,YAAYW,IAAAA,wBAAW,EAAC9B,QAAQ2B,OAChCE,cAAcD;IAElB,OAAO;QACLR,KAAKa;QACLN,IAAIK,gBAAgBE,aAAavB,IAAAA,wBAAW,EAACuB;IAC/C;AACF;AAEA,SAASC,oBAAoBhC,QAAgB,EAAEiC,KAAe;IAC5D,MAAMC,gBAAgBC,IAAAA,wCAAmB,EAACC,IAAAA,wCAAmB,EAACpC;IAC9D,IAAIkC,kBAAkB,UAAUA,kBAAkB,WAAW;QAC3D,OAAOlC;IACT;IAEA,2CAA2C;IAC3C,IAAI,CAACiC,MAAMI,QAAQ,CAACH,gBAAgB;QAClC,iDAAiD;QACjDD,MAAMtB,IAAI,CAAC,CAAC2B;YACV,IAAIC,IAAAA,yBAAc,EAACD,SAASE,IAAAA,yBAAa,EAACF,MAAMG,EAAE,CAAC1B,IAAI,CAACmB,gBAAgB;gBACtElC,WAAWsC;gBACX,OAAO;YACT;QACF;IACF;IACA,OAAOH,IAAAA,wCAAmB,EAACnC;AAC7B;AAEA,SAAS0C,kBACPC,MAAc,EACdC,QAAkB,EAClBnD,OAAkC;IAElC,MAAMoD,aAAa;QACjBC,UAAUrD,QAAQI,MAAM,CAACiD,QAAQ;QACjCC,MAAM;YAAEC,SAASvD,QAAQI,MAAM,CAACmD,OAAO;QAAC;QACxCC,eAAeC,QAAQC,QAAQC,GAAG,CAACC,qBAAqB;IAC1D;IACA,MAAMC,gBAAgBV,SAASW,OAAO,CAACC,GAAG,CAAC;IAE3C,IAAIC,gBACFH,iBAAiBV,SAASW,OAAO,CAACC,GAAG,CAAC;IAExC,MAAME,cAAcd,SAASW,OAAO,CAACC,GAAG,CAACG,8BAAmB;IAE5D,IACED,eACA,CAACD,iBACD,CAACC,YAAYrB,QAAQ,CAAC,2BACtB,CAACqB,YAAYrB,QAAQ,CAAC,cACtB,CAACqB,YAAYrB,QAAQ,CAAC,SACtB;QACA,4DAA4D;QAC5DoB,gBAAgBC;IAClB;IAEA,IAAID,eAAe;QACjB,IACEA,cAAcrC,UAAU,CAAC,QACzB+B,QAAQC,GAAG,CAACQ,0CAA0C,EACtD;YACA,MAAMC,sBAAsBC,IAAAA,kCAAgB,EAACL;YAC7C,MAAMM,eAAeC,IAAAA,wCAAmB,EAACH,oBAAoB7D,QAAQ,EAAE;gBACrE6C;gBACAoB,WAAW;YACb;YAEA,IAAIC,aAAa/B,IAAAA,wCAAmB,EAAC4B,aAAa/D,QAAQ;YAC1D,OAAOL,QAAQwE,GAAG,CAAC;gBACjB1E,QAAQI,MAAM,CAACC,UAAU,CAACsE,WAAW;gBACrCC,IAAAA,mCAAsB;aACvB,EAAEC,IAAI,CAAC;oBAAC,CAACrC,OAAO,EAAEsC,YAAYC,QAAQ,EAAE,CAAM;gBAC7C,IAAIhD,KAAKf,IAAAA,oBAAS,EAACsD,aAAa/D,QAAQ,EAAE+D,aAAarD,MAAM;gBAE7D,IACE6B,IAAAA,yBAAc,EAACf,OACd,CAAC8B,iBACArB,MAAMI,QAAQ,CACZoC,IAAAA,wCAAmB,EAACnE,IAAAA,8BAAc,EAACkB,KAAK/B,QAAQI,MAAM,CAACmD,OAAO,EAC3DhD,QAAQ,GAEf;oBACA,MAAM0E,eAAeV,IAAAA,wCAAmB,EACtCF,IAAAA,kCAAgB,EAACnB,QAAQ3C,QAAQ,EACjC;wBACE6C,YAAYM,QAAQC,GAAG,CAACuB,mBAAmB,GACvCC,YACA/B;wBACJoB,WAAW;oBACb;oBAGFzC,KAAKhB,IAAAA,wBAAW,EAACkE,aAAa1E,QAAQ;oBACtC6D,oBAAoB7D,QAAQ,GAAGwB;gBACjC;gBAEA,IAAI2B,QAAQC,GAAG,CAACuB,mBAAmB,EAAE;oBACnC,MAAME,SAASC,IAAAA,wBAAe,EAC5BtD,IACAS,OACAuC,UACAX,oBAAoBkB,KAAK,EACzB,CAACC,OAAiBhD,oBAAoBgD,MAAM/C,QAC5CxC,QAAQI,MAAM,CAACmD,OAAO;oBAGxB,IAAI6B,OAAOI,WAAW,EAAE;wBACtBpB,oBAAoB7D,QAAQ,GAAG6E,OAAOK,QAAQ,CAAClF,QAAQ;wBACvDwB,KAAKqC,oBAAoB7D,QAAQ;wBACjCX,OAAOC,MAAM,CAACuE,oBAAoBkB,KAAK,EAAEF,OAAOK,QAAQ,CAACH,KAAK;oBAChE;gBACF,OAAO,IAAI,CAAC9C,MAAMI,QAAQ,CAAC6B,aAAa;oBACtC,MAAMiB,mBAAmBnD,oBAAoBkC,YAAYjC;oBAEzD,IAAIkD,qBAAqBjB,YAAY;wBACnCA,aAAaiB;oBACf;gBACF;gBAEA,MAAM1D,eAAe,CAACQ,MAAMI,QAAQ,CAAC6B,cACjClC,oBACEyC,IAAAA,wCAAmB,EACjBnE,IAAAA,8BAAc,EAACuD,oBAAoB7D,QAAQ,GAC3CP,QAAQI,MAAM,CAACmD,OAAO,EACtBhD,QAAQ,EACViC,SAEFiC;gBAEJ,IAAI3B,IAAAA,yBAAc,EAACd,eAAe;oBAChC,MAAM2D,UAAUC,IAAAA,6BAAe,EAAC7C,IAAAA,yBAAa,EAACf,eAAeD;oBAC7DnC,OAAOC,MAAM,CAACuE,oBAAoBkB,KAAK,EAAEK,WAAW,CAAC;gBACvD;gBAEA,OAAO;oBACLE,MAAM;oBACNJ,UAAUrB;oBACVpC;gBACF;YACF;QACF;QACA,MAAM8D,MAAMrF,IAAAA,oBAAS,EAACyC;QACtB,MAAM3C,WAAWwF,IAAAA,8CAAsB,EAAC;YACtC,GAAGxB,IAAAA,wCAAmB,EAACuB,IAAIvF,QAAQ,EAAE;gBAAE6C;gBAAYoB,WAAW;YAAK,EAAE;YACrEwB,eAAehG,QAAQI,MAAM,CAAC4F,aAAa;YAC3CC,SAAS;QACX;QAEA,OAAO/F,QAAQC,OAAO,CAAC;YACrB0F,MAAM;YACNK,aAAa,AAAC,KAAE3F,WAAWuF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;QACjD;IACF;IAEA,MAAMC,iBAAiBjD,SAASW,OAAO,CAACC,GAAG,CAAC;IAE5C,IAAIqC,gBAAgB;QAClB,IAAIA,eAAezE,UAAU,CAAC,MAAM;YAClC,MAAMmE,MAAMrF,IAAAA,oBAAS,EAAC2F;YACtB,MAAM7F,WAAWwF,IAAAA,8CAAsB,EAAC;gBACtC,GAAGxB,IAAAA,wCAAmB,EAACuB,IAAIvF,QAAQ,EAAE;oBAAE6C;oBAAYoB,WAAW;gBAAK,EAAE;gBACrEwB,eAAehG,QAAQI,MAAM,CAAC4F,aAAa;gBAC3CC,SAAS;YACX;YAEA,OAAO/F,QAAQC,OAAO,CAAC;gBACrB0F,MAAM;gBACNQ,OAAO,AAAC,KAAE9F,WAAWuF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;gBACzCG,QAAQ,AAAC,KAAE/F,WAAWuF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;YAC5C;QACF;QAEA,OAAOjG,QAAQC,OAAO,CAAC;YACrB0F,MAAM;YACNK,aAAaE;QACf;IACF;IAEA,OAAOlG,QAAQC,OAAO,CAAC;QAAE0F,MAAM;IAAgB;AACjD;AAMA,eAAeU,sBACbvG,OAAkC;IAElC,MAAM2F,UAAU,MAAMjG,kBAAkBM;IACxC,IAAI,CAAC2F,WAAW,CAAC3F,QAAQwG,SAAS,EAAE;QAClC,OAAO;IACT;IAEA,MAAMC,OAAO,MAAMzG,QAAQwG,SAAS;IAEpC,MAAME,SAAS,MAAMzD,kBAAkBwD,KAAKE,QAAQ,EAAEF,KAAKtD,QAAQ,EAAEnD;IAErE,OAAO;QACL2G,UAAUF,KAAKE,QAAQ;QACvBC,MAAMH,KAAKG,IAAI;QACfzD,UAAUsD,KAAKtD,QAAQ;QACvB0D,MAAMJ,KAAKI,IAAI;QACfC,UAAUL,KAAKK,QAAQ;QACvBJ;IACF;AACF;AAyEA,MAAMK,0BACJrD,QAAQC,GAAG,CAACqD,yBAAyB,IACrC,OAAOC,WAAW,eAClB,uBAAuBA,OAAOC,OAAO,IACrC,CAAC,CAAC,AAAC;IACD,IAAI;QACF,IAAIC,IAAI;QACR,wCAAwC;QACxC,OAAOC,eAAeC,OAAO,CAACF,GAAGA,IAAIC,eAAeE,UAAU,CAACH,IAAI;IACrE,EAAE,OAAOI,GAAG,CAAC;AACf;AAEF,MAAMC,qBAAqBC,OAAO;AAElC,SAASC,WACPlG,GAAW,EACXmG,QAAgB,EAChB3H,OAAgD;IAEhD,OAAO4H,MAAMpG,KAAK;QAChB,sEAAsE;QACtE,yDAAyD;QACzD,EAAE;QACF,oEAAoE;QACpE,YAAY;QACZ,mEAAmE;QACnE,EAAE;QACF,iEAAiE;QACjE,sEAAsE;QACtE,8CAA8C;QAC9C,0CAA0C;QAC1CqG,aAAa;QACbC,QAAQ9H,QAAQ8H,MAAM,IAAI;QAC1BhE,SAASlE,OAAOC,MAAM,CAAC,CAAC,GAAGG,QAAQ8D,OAAO,EAAE;YAC1C,iBAAiB;QACnB;IACF,GAAGe,IAAI,CAAC,CAAC1B;QACP,OAAO,CAACA,SAAS4E,EAAE,IAAIJ,WAAW,KAAKxE,SAAS6E,MAAM,IAAI,MACtDN,WAAWlG,KAAKmG,WAAW,GAAG3H,WAC9BmD;IACN;AACF;AAsBA,SAAS8E,iBAAiBpB,IAAY;IACpC,IAAI;QACF,OAAOqB,KAAKC,KAAK,CAACtB;IACpB,EAAE,OAAOuB,OAAO;QACd,OAAO;IACT;AACF;AAEA,SAASC,cAAc,KAUD;IAVC,IAAA,EACrB1B,QAAQ,EACR2B,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,SAAS,EACTC,YAAY,EACZC,YAAY,EACZC,wBAAwB,EACJ,GAVC;IAWrB,MAAM,EAAEC,MAAMhC,QAAQ,EAAE,GAAG,IAAIiC,IAAIpC,UAAUM,OAAO+B,QAAQ,CAACF,IAAI;IACjE,MAAMG,UAAU,CAACC;YAULA;eATVxB,WAAWf,UAAU8B,iBAAiB,IAAI,GAAG;YAC3C3E,SAASlE,OAAOC,MAAM,CACpB,CAAC,GACD0I,aAAa;gBAAEY,SAAS;YAAW,IAAI,CAAC,GACxCZ,cAAcC,gBAAgB;gBAAE,yBAAyB;YAAI,IAAI,CAAC,GAClE9E,QAAQC,GAAG,CAACyF,kBAAkB,GAC1B;gBAAE,mBAAmB1F,QAAQC,GAAG,CAACyF,kBAAkB;YAAC,IACpD,CAAC;YAEPtB,QAAQoB,CAAAA,iBAAAA,0BAAAA,OAAQpB,MAAM,YAAdoB,iBAAkB;QAC5B,GACGrE,IAAI,CAAC,CAAC1B;YACL,IAAIA,SAAS4E,EAAE,IAAImB,CAAAA,0BAAAA,OAAQpB,MAAM,MAAK,QAAQ;gBAC5C,OAAO;oBAAEnB;oBAAUxD;oBAAU0D,MAAM;oBAAID,MAAM,CAAC;oBAAGE;gBAAS;YAC5D;YAEA,OAAO3D,SAAS0D,IAAI,GAAGhC,IAAI,CAAC,CAACgC;gBAC3B,IAAI,CAAC1D,SAAS4E,EAAE,EAAE;oBAChB;;;;;aAKC,GACD,IACES,iBACA;wBAAC;wBAAK;wBAAK;wBAAK;qBAAI,CAAC5F,QAAQ,CAACO,SAAS6E,MAAM,GAC7C;wBACA,OAAO;4BAAErB;4BAAUxD;4BAAU0D;4BAAMD,MAAM,CAAC;4BAAGE;wBAAS;oBACxD;oBAEA,IAAI3D,SAAS6E,MAAM,KAAK,KAAK;4BACvBC;wBAAJ,KAAIA,oBAAAA,iBAAiBpB,0BAAjBoB,kBAAwBoB,QAAQ,EAAE;4BACpC,OAAO;gCACL1C;gCACAC,MAAM;oCAAEyC,UAAU7B;gCAAmB;gCACrCrE;gCACA0D;gCACAC;4BACF;wBACF;oBACF;oBAEA,MAAMsB,QAAQ,qBAAwC,CAAxC,IAAItI,MAAO,gCAAX,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuC;oBAErD;;;;aAIC,GACD,IAAI,CAAC2I,gBAAgB;wBACnBa,IAAAA,2BAAc,EAAClB;oBACjB;oBAEA,MAAMA;gBACR;gBAEA,OAAO;oBACLzB;oBACAC,MAAM8B,YAAYT,iBAAiBpB,QAAQ;oBAC3C1D;oBACA0D;oBACAC;gBACF;YACF;QACF,GACCjC,IAAI,CAAC,CAAC4B;YACL,IACE,CAACkC,gBACDjF,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,gBACzB9C,KAAKtD,QAAQ,CAACW,OAAO,CAACC,GAAG,CAAC,0BAA0B,YACpD;gBACA,OAAOuE,aAAa,CAACxB,SAAS;YAChC;YACA,OAAOL;QACT,GACC+C,KAAK,CAAC,CAACC;YACN,IAAI,CAACZ,0BAA0B;gBAC7B,OAAOP,aAAa,CAACxB,SAAS;YAChC;YACA,IACE,SAAS;YACT2C,IAAIC,OAAO,KAAK,qBAChB,UAAU;YACVD,IAAIC,OAAO,KAAK,qDAChB,SAAS;YACTD,IAAIC,OAAO,KAAK,eAChB;gBACAJ,IAAAA,2BAAc,EAACG;YACjB;YACA,MAAMA;QACR;;IAEJ,+CAA+C;IAC/C,gDAAgD;IAChD,0DAA0D;IAC1D,2DAA2D;IAC3D,IAAIZ,4BAA4BF,cAAc;QAC5C,OAAOM,QAAQ,CAAC,GAAGpE,IAAI,CAAC,CAAC4B;YACvB,IAAIA,KAAKtD,QAAQ,CAACW,OAAO,CAACC,GAAG,CAAC,0BAA0B,YAAY;gBAClE,8CAA8C;gBAC9CuE,aAAa,CAACxB,SAAS,GAAG5G,QAAQC,OAAO,CAACsG;YAC5C;YAEA,OAAOA;QACT;IACF;IAEA,IAAI6B,aAAa,CAACxB,SAAS,KAAK3B,WAAW;QACzC,OAAOmD,aAAa,CAACxB,SAAS;IAChC;IACA,OAAQwB,aAAa,CAACxB,SAAS,GAAGmC,QAChCL,eAAe;QAAEd,QAAQ;IAAO,IAAI,CAAC;AAEzC;AAMO,SAAStI;IACd,OAAOmK,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C;AAEA,SAASC,qBAAqB,KAM7B;IAN6B,IAAA,EAC5BvI,GAAG,EACHpB,MAAM,EAIP,GAN6B;IAO5B,wDAAwD;IACxD,kDAAkD;IAClD,IAAIoB,QAAQT,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAACZ,OAAOM,MAAM,EAAEN,OAAOa,MAAM,IAAI;QAChE,MAAM,qBAEL,CAFK,IAAInB,MACR,AAAC,2DAAwD0B,MAAI,MAAGwH,SAASF,IAAI,GADzE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA7B,OAAO+B,QAAQ,CAACF,IAAI,GAAGtH;AACzB;AAEA,MAAMwI,sBAAsB;QAAC,EAC3BC,KAAK,EACL7J,MAAM,EAIP;IACC,IAAIL,YAAY;IAChB,MAAMmK,SAAU9J,OAAO+J,GAAG,GAAG;QAC3BpK,YAAY;IACd;IAEA,MAAMqK,kBAAkB;QACtB,IAAIrK,WAAW;YACb,MAAMqI,QAAa,qBAElB,CAFkB,IAAItI,MACrB,AAAC,0CAAuCmK,QAAM,MAD7B,qBAAA;uBAAA;4BAAA;8BAAA;YAEnB;YACA7B,MAAMrI,SAAS,GAAG;YAClB,MAAMqI;QACR;QAEA,IAAI8B,WAAW9J,OAAO+J,GAAG,EAAE;YACzB/J,OAAO+J,GAAG,GAAG;QACf;IACF;IACA,OAAOC;AACT;AAEe,MAAM3K;IA+SnB4K,SAAe;QACbpD,OAAO+B,QAAQ,CAACqB,MAAM;IACxB;IAEA;;GAEC,GACDC,OAAO;QACLrD,OAAOC,OAAO,CAACoD,IAAI;IACrB;IAEA;;GAEC,GACDC,UAAU;QACRtD,OAAOC,OAAO,CAACqD,OAAO;IACxB;IAEA;;;;;GAKC,GACDC,KAAKhJ,GAAQ,EAAEO,EAAQ,EAAE/B,OAA+B,EAAE;QAAjCA,IAAAA,oBAAAA,UAA6B,CAAC;QACrD,IAAI0D,QAAQC,GAAG,CAACqD,yBAAyB,EAAE;YACzC,wEAAwE;YACxE,iEAAiE;YACjE,IAAID,yBAAyB;gBAC3B,IAAI;oBACF,kEAAkE;oBAClEK,eAAeC,OAAO,CACpB,mBAAmB,IAAI,CAACoD,IAAI,EAC5BvC,KAAKwC,SAAS,CAAC;wBAAEC,GAAGC,KAAKC,WAAW;wBAAEC,GAAGF,KAAKG,WAAW;oBAAC;gBAE9D,EAAE,UAAM,CAAC;YACX;QACF;;QACE,CAAA,EAAEvJ,GAAG,EAAEO,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEN,KAAKO,GAAE;QAC1C,OAAO,IAAI,CAACiJ,MAAM,CAAC,aAAaxJ,KAAKO,IAAI/B;IAC3C;IAEA;;;;;GAKC,GACDiL,QAAQzJ,GAAQ,EAAEO,EAAQ,EAAE/B,OAA+B,EAAE;QAAjCA,IAAAA,oBAAAA,UAA6B,CAAC;;QACtD,CAAA,EAAEwB,GAAG,EAAEO,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEN,KAAKO,GAAE;QAC1C,OAAO,IAAI,CAACiJ,MAAM,CAAC,gBAAgBxJ,KAAKO,IAAI/B;IAC9C;IAEA,MAAMkL,KACJnJ,EAAU,EACVE,UAAmB,EACnBhB,MAAuB,EACvBkK,YAAsB,EACtB;QACA,IAAIzH,QAAQC,GAAG,CAACyH,mCAAmC,EAAE;YACnD,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;gBAChC,MAAM,EAAEC,WAAW,EAAE,GACnBC,QAAQ;gBAKV,IAAIC;gBACJ,IAAIC;gBAEJ,IAAI;;oBACA,CAAA,EACAC,sBAAsBF,gBAAgB,EACtCG,uBAAuBF,iBAAiB,EACzC,GAAI,MAAM9G,IAAAA,mCAAsB,GAGjC;gBACF,EAAE,OAAO6E,KAAK;oBACZ,8CAA8C;oBAC9C,aAAa;oBACboC,QAAQzD,KAAK,CAACqB;oBACd,IAAI0B,cAAc;wBAChB,OAAO;oBACT;oBACApB,qBAAqB;wBACnBvI,KAAKT,IAAAA,wBAAW,EACdC,IAAAA,oBAAS,EAACe,IAAId,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC+E,aAAa;wBAEzD5F,QAAQ,IAAI;oBACd;oBACA,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEA,MAAM4L,qBAAqCpI,QAAQC,GAAG,CACnDoI,6BAA6B;gBAEhC,IAAI,CAACN,oBAAoBK,oBAAoB;oBAC3CL,mBAAmBK,qBAAqBA,qBAAqB3G;gBAC/D;gBAEA,MAAM6G,qBAAqCtI,QAAQC,GAAG,CACnDsI,6BAA6B;gBAEhC,IAAI,CAACP,qBAAqBM,oBAAoB;oBAC5CN,oBAAoBM,qBAChBA,qBACA7G;gBACN;gBAEA,IAAIsG,oCAAAA,iBAAkBS,SAAS,EAAE;oBAC/B,IAAI,CAACb,MAAM,GAAG,IAAIE,YAChBE,iBAAiBU,QAAQ,EACzBV,iBAAiBW,SAAS;oBAE5B,IAAI,CAACf,MAAM,CAACgB,MAAM,CAACZ;gBACrB;gBAEA,IAAIC,qCAAAA,kBAAmBQ,SAAS,EAAE;oBAChC,IAAI,CAACZ,MAAM,GAAG,IAAIC,YAChBG,kBAAkBS,QAAQ,EAC1BT,kBAAkBU,SAAS;oBAE7B,IAAI,CAACd,MAAM,CAACe,MAAM,CAACX;gBACrB;YACF;YAEA,IAAIY,mBAAmB;YACvB,IAAIC,oBAAoB;YACxB,MAAMC,eACJ;gBAAC;oBAAEzK;gBAAG;gBAAG;oBAAEA,IAAIE;gBAAW;aAAE;YAE9B,KAAK,MAAM,EAAEF,IAAI0K,KAAK,EAAEC,iBAAiB,EAAE,IAAIF,aAAc;gBAC3D,IAAIC,OAAO;oBACT,MAAME,YAAYjK,IAAAA,wCAAmB,EACnC,IAAIqG,IAAI0D,OAAO,YAAYlM,QAAQ;oBAErC,MAAMqM,kBAAkB7L,IAAAA,wBAAW,EACjCC,IAAAA,oBAAS,EAAC2L,WAAW1L,UAAU,IAAI,CAACA,MAAM;oBAG5C,IACEyL,qBACAC,cACEjK,IAAAA,wCAAmB,EAAC,IAAIqG,IAAI,IAAI,CAACrI,MAAM,EAAE,YAAYH,QAAQ,GAC/D;4BAGI,cACA;wBAHJ+L,mBACEA,oBACA,CAAC,GAAC,eAAA,IAAI,CAACjB,MAAM,qBAAX,aAAawB,QAAQ,CAACF,eACxB,CAAC,GAAC,gBAAA,IAAI,CAACtB,MAAM,qBAAX,cAAawB,QAAQ,CAACD;wBAE1B,KAAK,MAAME,gBAAgB;4BAACH;4BAAWC;yBAAgB,CAAE;4BACvD,sDAAsD;4BACtD,8BAA8B;4BAC9B,MAAMG,aAAaD,aAAaE,KAAK,CAAC;4BACtC,IACE,IAAIC,IAAI,GACR,CAACV,qBAAqBU,IAAIF,WAAWlL,MAAM,GAAG,GAC9CoL,IACA;oCAEmB;gCADnB,MAAMC,cAAcH,WAAWjD,KAAK,CAAC,GAAGmD,GAAGE,IAAI,CAAC;gCAChD,IAAID,iBAAe,eAAA,IAAI,CAAC5B,MAAM,qBAAX,aAAauB,QAAQ,CAACK,eAAc;oCACrDX,oBAAoB;oCACpB;gCACF;4BACF;wBACF;wBAEA,yDAAyD;wBACzD,oBAAoB;wBACpB,IAAID,oBAAoBC,mBAAmB;4BACzC,IAAIpB,cAAc;gCAChB,OAAO;4BACT;4BACApB,qBAAqB;gCACnBvI,KAAKT,IAAAA,wBAAW,EACdC,IAAAA,oBAAS,EAACe,IAAId,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC+E,aAAa;gCAEzD5F,QAAQ,IAAI;4BACd;4BACA,OAAO,IAAIF,QAAQ,KAAO;wBAC5B;oBACF;gBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAc8K,OACZlD,MAAqB,EACrBtG,GAAW,EACXO,EAAU,EACV/B,OAA0B,EAC1BoN,YAAuC,EACrB;YA8Ob;QA7OL,IAAI,CAACC,IAAAA,sBAAU,EAAC7L,MAAM;YACpBuI,qBAAqB;gBAAEvI;gBAAKpB,QAAQ,IAAI;YAAC;YACzC,OAAO;QACT;QACA,sEAAsE;QACtE,yEAAyE;QACzE,2BAA2B;QAC3B,MAAMkN,kBAAkB,AAACtN,QAAgBuN,EAAE,KAAK;QAEhD,IAAI,CAACD,mBAAmB,CAACtN,QAAQwN,OAAO,EAAE;YACxC,MAAM,IAAI,CAACtC,IAAI,CAACnJ,IAAIoD,WAAWnF,QAAQiB,MAAM;QAC/C;QAEA,IAAIwM,oBACFH,mBACA,AAACtN,QAAgB0N,kBAAkB,IACnCjN,IAAAA,oBAAS,EAACe,KAAKjB,QAAQ,KAAKE,IAAAA,oBAAS,EAACsB,IAAIxB,QAAQ;QAEpD,MAAMoN,YAAY;YAChB,GAAG,IAAI,CAACC,KAAK;QACf;QAEA,yDAAyD;QACzD,4DAA4D;QAC5D,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACC,OAAO,KAAK;QAC1C,IAAI,CAACA,OAAO,GAAG;QACf,MAAMC,QAAQ,IAAI,CAACA,KAAK;QAExB,IAAI,CAACT,iBAAiB;YACpB,IAAI,CAACS,KAAK,GAAG;QACf;QAEA,sDAAsD;QACtD,wDAAwD;QACxD,IAAIT,mBAAmB,IAAI,CAACnD,GAAG,EAAE;YAC/B,OAAO;QACT;QAEA,MAAM6D,aAAaL,UAAU1M,MAAM;QAEnC,IAAIyC,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;YACnCN,UAAU1M,MAAM,GACdjB,QAAQiB,MAAM,KAAK,QACf,IAAI,CAAC+E,aAAa,GAClBhG,QAAQiB,MAAM,IAAI0M,UAAU1M,MAAM;YAExC,IAAI,OAAOjB,QAAQiB,MAAM,KAAK,aAAa;gBACzCjB,QAAQiB,MAAM,GAAG0M,UAAU1M,MAAM;YACnC;YAEA,MAAMwE,WAAWpB,IAAAA,kCAAgB,EAC/BzD,IAAAA,wBAAW,EAACmB,MAAMlB,IAAAA,8BAAc,EAACkB,MAAMA;YAEzC,MAAMmM,mBAAmBlJ,IAAAA,wCAAmB,EAC1CS,SAASlF,QAAQ,EACjB,IAAI,CAACgD,OAAO;YAGd,IAAI2K,iBAAiBC,cAAc,EAAE;gBACnCR,UAAU1M,MAAM,GAAGiN,iBAAiBC,cAAc;gBAClD1I,SAASlF,QAAQ,GAAGQ,IAAAA,wBAAW,EAAC0E,SAASlF,QAAQ;gBACjDwB,KAAKqM,IAAAA,+BAAoB,EAAC3I;gBAC1BjE,MAAMT,IAAAA,wBAAW,EACfiE,IAAAA,wCAAmB,EACjBpE,IAAAA,wBAAW,EAACY,OAAOX,IAAAA,8BAAc,EAACW,OAAOA,KACzC,IAAI,CAAC+B,OAAO,EACZhD,QAAQ;YAEd;YACA,IAAI8N,cAAc;YAElB,wEAAwE;YACxE,0CAA0C;YAC1C,IAAI3K,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;oBAE9B;gBADL,gEAAgE;gBAChE,IAAI,GAAC,gBAAA,IAAI,CAAC1K,OAAO,qBAAZ,cAAcX,QAAQ,CAAC+K,UAAU1M,MAAM,IAAI;oBAC9CwE,SAASlF,QAAQ,GAAGS,IAAAA,oBAAS,EAACyE,SAASlF,QAAQ,EAAEoN,UAAU1M,MAAM;oBACjE8I,qBAAqB;wBACnBvI,KAAK4M,IAAAA,+BAAoB,EAAC3I;wBAC1BrF,QAAQ,IAAI;oBACd;oBACA,wDAAwD;oBACxD,2DAA2D;oBAC3DiO,cAAc;gBAChB;YACF;YAEA,MAAMC,iBAAiBC,IAAAA,sCAAkB,EACvC,IAAI,CAACC,aAAa,EAClBrJ,WACAwI,UAAU1M,MAAM;YAGlB,wEAAwE;YACxE,0CAA0C;YAC1C,IAAIyC,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;gBACnC,oEAAoE;gBACpE,iBAAiB;gBACjB,IACE,CAACI,eACDC,kBACA,IAAI,CAACG,cAAc,IACnB7D,KAAK5B,QAAQ,CAAC0F,QAAQ,KAAKJ,eAAeK,MAAM,EAChD;oBACA,MAAMC,eAAe/N,IAAAA,8BAAc,EAACkB;oBACpCgI,qBAAqB;wBACnBvI,KAAK,AAAC,SAAM8M,CAAAA,eAAeO,IAAI,GAAG,KAAK,GAAE,IAAE,QACzCP,eAAeK,MAAM,GACpB5N,IAAAA,wBAAW,EACZ,AAAC,KACC4M,CAAAA,UAAU1M,MAAM,KAAKqN,eAAetI,aAAa,GAC7C,KACA,AAAC,MAAG2H,UAAU1M,MAAM,AAAC,IACxB2N,CAAAA,iBAAiB,MAAM,KAAKA,YAAW,KAAO;wBAEnDxO,QAAQ,IAAI;oBACd;oBACA,wDAAwD;oBACxD,2DAA2D;oBAC3DiO,cAAc;gBAChB;YACF;YAEA,IAAIA,aAAa;gBACf,OAAO,IAAInO,QAAQ,KAAO;YAC5B;QACF;QAEA,oDAAoD;QACpD,IAAI4O,SAAE,EAAE;YACNC,YAAYC,IAAI,CAAC;QACnB;QAEA,MAAM,EAAExB,UAAU,KAAK,EAAEyB,SAAS,IAAI,EAAE,GAAGjP;QAC3C,MAAMkP,aAAa;YAAE1B;QAAQ;QAE7B,IAAI,IAAI,CAAC2B,cAAc,IAAI,IAAI,CAAChF,GAAG,EAAE;YACnC,IAAI,CAAC4D,OAAO;gBACVtO,OAAO2P,MAAM,CAACC,IAAI,CAChB,oBACA1P,0BACA,IAAI,CAACwP,cAAc,EACnBD;YAEJ;YACA,IAAI,CAAC/E,GAAG;YACR,IAAI,CAACA,GAAG,GAAG;QACb;QAEApI,KAAKhB,IAAAA,wBAAW,EACdC,IAAAA,oBAAS,EACPJ,IAAAA,wBAAW,EAACmB,MAAMlB,IAAAA,8BAAc,EAACkB,MAAMA,IACvC/B,QAAQiB,MAAM,EACd,IAAI,CAAC+E,aAAa;QAGtB,MAAMrF,YAAY2O,IAAAA,0BAAY,EAC5B1O,IAAAA,wBAAW,EAACmB,MAAMlB,IAAAA,8BAAc,EAACkB,MAAMA,IACvC4L,UAAU1M,MAAM;QAElB,IAAI,CAACkO,cAAc,GAAGpN;QAEtB,MAAMwN,eAAevB,eAAeL,UAAU1M,MAAM;QAEpD,qDAAqD;QACrD,0DAA0D;QAE1D,IAAI,CAACqM,mBAAmB,IAAI,CAACkC,eAAe,CAAC7O,cAAc,CAAC4O,cAAc;YACxE5B,UAAUjN,MAAM,GAAGC;YACnBlB,OAAO2P,MAAM,CAACC,IAAI,CAAC,mBAAmBtN,IAAImN;YAC1C,8DAA8D;YAC9D,IAAI,CAACO,WAAW,CAAC3H,QAAQtG,KAAKO,IAAI;gBAChC,GAAG/B,OAAO;gBACViP,QAAQ;YACV;YACA,IAAIA,QAAQ;gBACV,IAAI,CAACS,YAAY,CAAC/O;YACpB;YACA,IAAI;gBACF,MAAM,IAAI,CAACgP,GAAG,CAAChC,WAAW,IAAI,CAACiC,UAAU,CAACjC,UAAU1D,KAAK,CAAC,EAAE;YAC9D,EAAE,OAAOR,KAAK;gBACZ,IAAIoG,IAAAA,gBAAO,EAACpG,QAAQA,IAAI1J,SAAS,EAAE;oBACjCN,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoB5F,KAAK9I,WAAWuO;gBACzD;gBACA,MAAMzF;YACR;YAEAhK,OAAO2P,MAAM,CAACC,IAAI,CAAC,sBAAsBtN,IAAImN;YAC7C,OAAO;QACT;QAEA,IAAIY,SAASzL,IAAAA,kCAAgB,EAAC7C;QAC9B,IAAI,EAAEjB,QAAQ,EAAE+E,KAAK,EAAE,GAAGwK;QAE1B,yEAAyE;QACzE,2EAA2E;QAC3E,oBAAoB;QACpB,IAAItN,OAAiBuC;QACrB,IAAI;;YACD,CAACvC,OAAO,EAAEsC,YAAYC,QAAQ,EAAE,CAAC,GAAG,MAAM7E,QAAQwE,GAAG,CAAC;gBACrD,IAAI,CAACrE,UAAU,CAACsE,WAAW;gBAC3BC,IAAAA,mCAAsB;gBACtB,IAAI,CAACvE,UAAU,CAACC,aAAa;aAC9B;QACH,EAAE,OAAOmJ,KAAK;YACZ,wEAAwE;YACxE,+BAA+B;YAC/BM,qBAAqB;gBAAEvI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA,uEAAuE;QACvE,8EAA8E;QAC9E,uDAAuD;QACvD,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC2P,QAAQ,CAACpP,cAAc,CAAC4O,cAAc;YAC9CzH,SAAS;QACX;QAEA,iEAAiE;QACjE,iDAAiD;QACjD,IAAI7F,aAAaF;QAEjB,6DAA6D;QAC7D,gEAAgE;QAChE,2DAA2D;QAC3DxB,WAAWA,WACPmC,IAAAA,wCAAmB,EAAC7B,IAAAA,8BAAc,EAACN,aACnCA;QAEJ,IAAI0J,QAAQvH,IAAAA,wCAAmB,EAACnC;QAChC,MAAMyP,mBAAmBjO,GAAGJ,UAAU,CAAC,QAAQ0C,IAAAA,kCAAgB,EAACtC,IAAIxB,QAAQ;QAE5E,0DAA0D;QAC1D,0BAA0B;QAC1B,KAAK,4BAAA,IAAI,CAACqP,UAAU,CAACrP,SAAS,qBAA1B,AAAC,0BAAmC0P,WAAW,EAAE;YACnDlG,qBAAqB;gBAAEvI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO,IAAIF,QAAQ,KAAO;QAC5B;QAEA,MAAMgQ,sBAAsB,CAAC,CAC3BF,CAAAA,oBACA/F,UAAU+F,oBACT,CAAA,CAAClN,IAAAA,yBAAc,EAACmH,UACf,CAACrE,IAAAA,6BAAe,EAAC7C,IAAAA,yBAAa,EAACkH,QAAQ+F,iBAAgB,CAAC;QAG5D,0DAA0D;QAC1D,qDAAqD;QACrD,MAAMG,oBACJ,CAACnQ,QAAQwN,OAAO,IACf,MAAM9N,kBAAkB;YACvBgB,QAAQqB;YACRd,QAAQ0M,UAAU1M,MAAM;YACxBb,QAAQ,IAAI;QACd;QAEF,IAAIkN,mBAAmB6C,mBAAmB;YACxC1C,oBAAoB;QACtB;QAEA,IAAIA,qBAAqBlN,aAAa,WAAW;;YAC7CP,QAAgB0N,kBAAkB,GAAG;YAEvC,IAAIhK,QAAQC,GAAG,CAACuB,mBAAmB,IAAInD,GAAGJ,UAAU,CAAC,MAAM;gBACzD,MAAMyO,iBAAiB/K,IAAAA,wBAAe,EACpCtE,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAACL,WAAWgN,UAAU1M,MAAM,GAAG,OACpDuB,OACAuC,UACAO,OACA,CAAC+K,IAAc9N,oBAAoB8N,GAAG7N,QACtC,IAAI,CAACe,OAAO;gBAGd,IAAI6M,eAAeE,YAAY,EAAE;oBAC/BvG,qBAAqB;wBAAEvI,KAAKO;wBAAI3B,QAAQ,IAAI;oBAAC;oBAC7C,OAAO;gBACT;gBACA,IAAI,CAAC+P,mBAAmB;oBACtBlO,aAAamO,eAAe1P,MAAM;gBACpC;gBAEA,IAAI0P,eAAe5K,WAAW,IAAI4K,eAAepO,YAAY,EAAE;oBAC7D,gEAAgE;oBAChE,4CAA4C;oBAC5CzB,WAAW6P,eAAepO,YAAY;oBACtC8N,OAAOvP,QAAQ,GAAGQ,IAAAA,wBAAW,EAACR;oBAE9B,IAAI,CAAC4P,mBAAmB;wBACtB3O,MAAM4M,IAAAA,+BAAoB,EAAC0B;oBAC7B;gBACF;YACF,OAAO;gBACLA,OAAOvP,QAAQ,GAAGgC,oBAAoBhC,UAAUiC;gBAEhD,IAAIsN,OAAOvP,QAAQ,KAAKA,UAAU;oBAChCA,WAAWuP,OAAOvP,QAAQ;oBAC1BuP,OAAOvP,QAAQ,GAAGQ,IAAAA,wBAAW,EAACR;oBAE9B,IAAI,CAAC4P,mBAAmB;wBACtB3O,MAAM4M,IAAAA,+BAAoB,EAAC0B;oBAC7B;gBACF;YACF;QACF;QAEA,IAAI,CAACzC,IAAAA,sBAAU,EAACtL,KAAK;YACnB,IAAI2B,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,cAAc;gBACzC,MAAM,qBAGL,CAHK,IAAIzJ,MACR,AAAC,oBAAiB0B,MAAI,gBAAaO,KAAG,8CACnC,uFAFC,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;YACAgI,qBAAqB;gBAAEvI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA6B,aAAaqN,IAAAA,0BAAY,EAACzO,IAAAA,8BAAc,EAACoB,aAAa0L,UAAU1M,MAAM;QAEtEgJ,QAAQvH,IAAAA,wCAAmB,EAACnC;QAC5B,IAAIgQ,aAA6B;QAEjC,IAAIzN,IAAAA,yBAAc,EAACmH,QAAQ;YACzB,MAAMxE,WAAWpB,IAAAA,kCAAgB,EAACpC;YAClC,MAAMzB,aAAaiF,SAASlF,QAAQ;YAEpC,MAAMiQ,aAAazN,IAAAA,yBAAa,EAACkH;YACjCsG,aAAa3K,IAAAA,6BAAe,EAAC4K,YAAYhQ;YACzC,MAAMiQ,oBAAoBxG,UAAUzJ;YACpC,MAAMkQ,iBAAiBD,oBACnBE,IAAAA,4BAAa,EAAC1G,OAAOzJ,YAAY8E,SAChC,CAAC;YAEN,IAAI,CAACiL,cAAeE,qBAAqB,CAACC,eAAetL,MAAM,EAAG;gBAChE,MAAMwL,gBAAgBhR,OAAOiR,IAAI,CAACL,WAAWM,MAAM,EAAEC,MAAM,CACzD,CAACC,QAAU,CAAC1L,KAAK,CAAC0L,MAAM,IAAI,CAACR,WAAWM,MAAM,CAACE,MAAM,CAACC,QAAQ;gBAGhE,IAAIL,cAAc/O,MAAM,GAAG,KAAK,CAACsO,mBAAmB;oBAClD,IAAIzM,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,cAAc;wBACzCsC,QAAQqF,IAAI,CACV,AAAC,KACCT,CAAAA,oBACK,uBACA,6BAA+B,IACrC,iCACC,CAAA,AAAC,iBAAcG,cAAczD,IAAI,CAC/B,QACA,0BAA4B;oBAEpC;oBAEA,MAAM,qBAWL,CAXK,IAAIrN,MACR,AAAC2Q,CAAAA,oBACG,AAAC,0BAAyBjP,MAAI,sCAAmCoP,cAAczD,IAAI,CACjF,QACA,oCACF,AAAC,8BAA6B3M,aAAW,8CAA6CyJ,QAAM,KAAG,IACjG,CAAA,AAAC,iDACCwG,CAAAA,oBACI,8BACA,sBAAqB,CAC1B,IAVC,qBAAA;+BAAA;oCAAA;sCAAA;oBAWN;gBACF;YACF,OAAO,IAAIA,mBAAmB;gBAC5B1O,KAAKqM,IAAAA,+BAAoB,EACvBxO,OAAOC,MAAM,CAAC,CAAC,GAAG4F,UAAU;oBAC1BlF,UAAUmQ,eAAetL,MAAM;oBAC/BE,OAAO6L,IAAAA,UAAI,EAAC7L,OAAOoL,eAAexH,MAAM;gBAC1C;YAEJ,OAAO;gBACL,iEAAiE;gBACjEtJ,OAAOC,MAAM,CAACyF,OAAOiL;YACvB;QACF;QAEA,IAAI,CAACjD,iBAAiB;YACpB7N,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoBtN,IAAImN;QAC7C;QAEA,MAAMkC,eAAe,IAAI,CAAC7Q,QAAQ,KAAK,UAAU,IAAI,CAACA,QAAQ,KAAK;QAEnE,IAAI;gBAsKAqK,qCAAAA,2BACAyG;YAtKF,IAAIA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;gBACtCrH;gBACA1J;gBACA+E;gBACAvD;gBACAE;gBACAiN;gBACAjO,QAAQ0M,UAAU1M,MAAM;gBACxBsQ,WAAW5D,UAAU4D,SAAS;gBAC9B/I,eAAe2H;gBACftH,0BAA0B7I,QAAQ6I,wBAAwB;gBAC1DyE,iBAAiBA,mBAAmB,CAAC,IAAI,CAACkE,UAAU;gBACpDtB;YACF;YAEA,IAAI,CAAC5C,mBAAmB,CAACtN,QAAQwN,OAAO,EAAE;gBACxC,MAAM,IAAI,CAACtC,IAAI,CACbnJ,IACA,gBAAgBsP,YAAYA,UAAUpP,UAAU,GAAGkD,WACnDwI,UAAU1M,MAAM;YAEpB;YAEA,IAAI,WAAWoQ,aAAalB,mBAAmB;gBAC7C5P,WAAW8Q,UAAUpH,KAAK,IAAIA;gBAC9BA,QAAQ1J;gBAER,IAAI,CAAC2O,WAAW1B,OAAO,EAAE;oBACvBlI,QAAQ1F,OAAOC,MAAM,CAAC,CAAC,GAAGwR,UAAU/L,KAAK,IAAI,CAAC,GAAGA;gBACnD;gBAEA,MAAMmM,wBAAwB7Q,IAAAA,wBAAW,EAACkP,OAAOvP,QAAQ,IACrDM,IAAAA,8BAAc,EAACiP,OAAOvP,QAAQ,IAC9BuP,OAAOvP,QAAQ;gBAEnB,IAAIgQ,cAAchQ,aAAakR,uBAAuB;oBACpD7R,OAAOiR,IAAI,CAACN,YAAYmB,OAAO,CAAC,CAACC;wBAC/B,IAAIpB,cAAcjL,KAAK,CAACqM,IAAI,KAAKpB,UAAU,CAACoB,IAAI,EAAE;4BAChD,OAAOrM,KAAK,CAACqM,IAAI;wBACnB;oBACF;gBACF;gBAEA,IAAI7O,IAAAA,yBAAc,EAACvC,WAAW;oBAC5B,MAAMqR,aACJ,CAAC1C,WAAW1B,OAAO,IAAI6D,UAAUpP,UAAU,GACvCoP,UAAUpP,UAAU,GACpBlB,IAAAA,wBAAW,EACTC,IAAAA,oBAAS,EACP,IAAI+H,IAAIhH,IAAIiH,SAASF,IAAI,EAAEvI,QAAQ,EACnCoN,UAAU1M,MAAM,GAElB;oBAGR,IAAI4Q,YAAYD;oBAEhB,IAAIhR,IAAAA,wBAAW,EAACiR,YAAY;wBAC1BA,YAAYhR,IAAAA,8BAAc,EAACgR;oBAC7B;oBAEA,IAAInO,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;wBACnC,MAAM6D,eAAe9M,IAAAA,wCAAmB,EAAC6M,WAAW,IAAI,CAACtO,OAAO;wBAChEoK,UAAU1M,MAAM,GAAG6Q,aAAa3D,cAAc,IAAIR,UAAU1M,MAAM;wBAClE4Q,YAAYC,aAAavR,QAAQ;oBACnC;oBACA,MAAMiQ,aAAazN,IAAAA,yBAAa,EAACxC;oBACjC,MAAMwR,gBAAgBnM,IAAAA,6BAAe,EAAC4K,YACpC,IAAIzH,IAAI8I,WAAW7I,SAASF,IAAI,EAAEvI,QAAQ;oBAG5C,IAAIwR,eAAe;wBACjBnS,OAAOC,MAAM,CAACyF,OAAOyM;oBACvB;gBACF;YACF;YAEA,yDAAyD;YACzD,IAAI,UAAUV,WAAW;gBACvB,IAAIA,UAAUxL,IAAI,KAAK,qBAAqB;oBAC1C,OAAO,IAAI,CAACmF,MAAM,CAAClD,QAAQuJ,UAAU/K,MAAM,EAAE+K,UAAUhL,KAAK,EAAErG;gBAChE,OAAO;oBACL+J,qBAAqB;wBAAEvI,KAAK6P,UAAUnL,WAAW;wBAAE9F,QAAQ,IAAI;oBAAC;oBAChE,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;YACF;YAEA,MAAM8R,YAAiBX,UAAUY,SAAS;YAC1C,IAAID,aAAaA,UAAUE,qBAAqB,EAAE;gBAChD,MAAMC,UAAU,EAAE,CAACC,MAAM,CAACJ,UAAUE,qBAAqB;gBAEzDC,QAAQT,OAAO,CAAC,CAACW;oBACfC,IAAAA,8BAAsB,EAACD,OAAOE,KAAK;gBACrC;YACF;YAEA,uCAAuC;YACvC,IAAI,AAAClB,CAAAA,UAAUmB,OAAO,IAAInB,UAAUoB,OAAO,AAAD,KAAMpB,UAAUkB,KAAK,EAAE;gBAC/D,IACElB,UAAUkB,KAAK,CAACG,SAAS,IACzBrB,UAAUkB,KAAK,CAACG,SAAS,CAACC,YAAY,EACtC;oBACA,0DAA0D;oBAC1D3S,QAAQiB,MAAM,GAAG;oBAEjB,MAAMiF,cAAcmL,UAAUkB,KAAK,CAACG,SAAS,CAACC,YAAY;oBAE1D,oEAAoE;oBACpE,gEAAgE;oBAChE,WAAW;oBACX,IACEzM,YAAYvE,UAAU,CAAC,QACvB0P,UAAUkB,KAAK,CAACG,SAAS,CAACE,sBAAsB,KAAK,OACrD;wBACA,MAAMC,aAAaxO,IAAAA,kCAAgB,EAAC6B;wBACpC2M,WAAWtS,QAAQ,GAAGgC,oBACpBsQ,WAAWtS,QAAQ,EACnBiC;wBAGF,MAAM,EAAEhB,KAAK8E,MAAM,EAAEvE,IAAIsE,KAAK,EAAE,GAAGvE,aACjC,IAAI,EACJoE,aACAA;wBAEF,OAAO,IAAI,CAAC8E,MAAM,CAAClD,QAAQxB,QAAQD,OAAOrG;oBAC5C;oBACA+J,qBAAqB;wBAAEvI,KAAK0E;wBAAa9F,QAAQ,IAAI;oBAAC;oBACtD,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEAyN,UAAU4D,SAAS,GAAG,CAAC,CAACF,UAAUkB,KAAK,CAACO,WAAW;gBAEnD,sBAAsB;gBACtB,IAAIzB,UAAUkB,KAAK,CAAClJ,QAAQ,KAAK7B,oBAAoB;oBACnD,IAAIuL;oBAEJ,IAAI;wBACF,MAAM,IAAI,CAACC,cAAc,CAAC;wBAC1BD,gBAAgB;oBAClB,EAAE,OAAOE,GAAG;wBACVF,gBAAgB;oBAClB;oBAEA1B,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;wBAClCrH,OAAO8I;wBACPxS,UAAUwS;wBACVzN;wBACAvD;wBACAE;wBACAiN,YAAY;4BAAE1B,SAAS;wBAAM;wBAC7BvM,QAAQ0M,UAAU1M,MAAM;wBACxBsQ,WAAW5D,UAAU4D,SAAS;wBAC9B2B,YAAY;oBACd;oBAEA,IAAI,UAAU7B,WAAW;wBACvB,MAAM,qBAAiD,CAAjD,IAAIvR,MAAO,yCAAX,qBAAA;mCAAA;wCAAA;0CAAA;wBAAgD;oBACxD;gBACF;YACF;YAEA,IACEwN,mBACA,IAAI,CAAC/M,QAAQ,KAAK,aAClBqK,EAAAA,4BAAAA,KAAKuI,aAAa,CAACZ,KAAK,sBAAxB3H,sCAAAA,0BAA0B8H,SAAS,qBAAnC9H,oCAAqCwI,UAAU,MAAK,SACpD/B,mBAAAA,UAAUkB,KAAK,qBAAflB,iBAAiBqB,SAAS,GAC1B;gBACA,yDAAyD;gBACzD,kCAAkC;gBAClCrB,UAAUkB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;YACzC;gBAI0C/B;YAF1C,6DAA6D;YAC7D,MAAMgC,sBACJrT,QAAQwN,OAAO,IAAIG,UAAU1D,KAAK,KAAMoH,CAAAA,CAAAA,mBAAAA,UAAUpH,KAAK,YAAfoH,mBAAmBpH,KAAI;gBAG/DjK;YADF,MAAMsT,eACJtT,CAAAA,kBAAAA,QAAQiP,MAAM,YAAdjP,kBAAmB,CAACsN,mBAAmB,CAAC+F;YAC1C,MAAME,cAAcD,eAAe;gBAAE3I,GAAG;gBAAGG,GAAG;YAAE,IAAI;YACpD,MAAM0I,sBAAsBpG,uBAAAA,eAAgBmG;YAE5C,0CAA0C;YAC1C,MAAME,sBAAsB;gBAC1B,GAAG9F,SAAS;gBACZ1D;gBACA1J;gBACA+E;gBACA5E,QAAQC;gBACR6Q,YAAY;YACd;YAEA,0EAA0E;YAC1E,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,YAAY;YACZ,IAAIlE,mBAAmB8D,cAAc;oBAmBjCxG,sCAAAA,4BACAyG;gBAnBFA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;oBAClCrH,OAAO,IAAI,CAAC1J,QAAQ;oBACpBA,UAAU,IAAI,CAACA,QAAQ;oBACvB+E;oBACAvD;oBACAE;oBACAiN,YAAY;wBAAE1B,SAAS;oBAAM;oBAC7BvM,QAAQ0M,UAAU1M,MAAM;oBACxBsQ,WAAW5D,UAAU4D,SAAS;oBAC9BjE,iBAAiBA,mBAAmB,CAAC,IAAI,CAACkE,UAAU;gBACtD;gBAEA,IAAI,UAAUH,WAAW;oBACvB,MAAM,qBAA6D,CAA7D,IAAIvR,MAAM,AAAC,qCAAkC,IAAI,CAACS,QAAQ,GAA1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4D;gBACpE;gBAEA,IACE,IAAI,CAACA,QAAQ,KAAK,aAClBqK,EAAAA,6BAAAA,KAAKuI,aAAa,CAACZ,KAAK,sBAAxB3H,uCAAAA,2BAA0B8H,SAAS,qBAAnC9H,qCAAqCwI,UAAU,MAAK,SACpD/B,oBAAAA,UAAUkB,KAAK,qBAAflB,kBAAiBqB,SAAS,GAC1B;oBACA,yDAAyD;oBACzD,kCAAkC;oBAClCrB,UAAUkB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;gBACzC;gBAEA,IAAI;oBACF,MAAM,IAAI,CAACzD,GAAG,CAAC8D,qBAAqBpC,WAAWmC;gBACjD,EAAE,OAAO/J,KAAK;oBACZ,IAAIoG,IAAAA,gBAAO,EAACpG,QAAQA,IAAI1J,SAAS,EAAE;wBACjCN,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoB5F,KAAK9I,WAAWuO;oBACzD;oBACA,MAAMzF;gBACR;gBAEA,OAAO;YACT;YAEAhK,OAAO2P,MAAM,CAACC,IAAI,CAAC,uBAAuBtN,IAAImN;YAC9C,IAAI,CAACO,WAAW,CAAC3H,QAAQtG,KAAKO,IAAI/B;YAElC,0EAA0E;YAC1E,iBAAiB;YACjB,iDAAiD;YACjD,MAAM0T,kBACJpG,mBACA,CAACkG,uBACD,CAAC3F,oBACD,CAAC0B,gBACDoE,IAAAA,kCAAmB,EAACF,qBAAqB,IAAI,CAAC7F,KAAK;YAErD,IAAI,CAAC8F,iBAAiB;gBACpB,IAAI;oBACF,MAAM,IAAI,CAAC/D,GAAG,CAAC8D,qBAAqBpC,WAAWmC;gBACjD,EAAE,OAAOI,GAAQ;oBACf,IAAIA,EAAE7T,SAAS,EAAEsR,UAAUjJ,KAAK,GAAGiJ,UAAUjJ,KAAK,IAAIwL;yBACjD,MAAMA;gBACb;gBAEA,IAAIvC,UAAUjJ,KAAK,EAAE;oBACnB,IAAI,CAACkF,iBAAiB;wBACpB7N,OAAO2P,MAAM,CAACC,IAAI,CAChB,oBACAgC,UAAUjJ,KAAK,EACfzH,WACAuO;oBAEJ;oBAEA,MAAMmC,UAAUjJ,KAAK;gBACvB;gBAEA,IAAI1E,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;oBACnC,IAAIN,UAAU1M,MAAM,EAAE;wBACpB4S,SAASC,eAAe,CAACC,IAAI,GAAGpG,UAAU1M,MAAM;oBAClD;gBACF;gBAEA,IAAI,CAACqM,iBAAiB;oBACpB7N,OAAO2P,MAAM,CAACC,IAAI,CAAC,uBAAuBtN,IAAImN;gBAChD;gBAEA,mDAAmD;gBACnD,MAAM8E,YAAY;gBAClB,IAAIV,gBAAgBU,UAAU1S,IAAI,CAACS,KAAK;oBACtC,IAAI,CAAC2N,YAAY,CAAC3N;gBACpB;YACF;YAEA,OAAO;QACT,EAAE,OAAO0H,KAAK;YACZ,IAAIoG,IAAAA,gBAAO,EAACpG,QAAQA,IAAI1J,SAAS,EAAE;gBACjC,OAAO;YACT;YACA,MAAM0J;QACR;IACF;IAEAgG,YACE3H,MAAqB,EACrBtG,GAAW,EACXO,EAAU,EACV/B,OAA+B,EACzB;QADNA,IAAAA,oBAAAA,UAA6B,CAAC;QAE9B,IAAI0D,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,cAAc;YACzC,IAAI,OAAOtC,OAAOC,OAAO,KAAK,aAAa;gBACzC2E,QAAQzD,KAAK,CAAE;gBACf;YACF;YAEA,IAAI,OAAOnB,OAAOC,OAAO,CAACY,OAAO,KAAK,aAAa;gBACjD+D,QAAQzD,KAAK,CAAC,AAAC,6BAA0BN,SAAO;gBAChD;YACF;QACF;QAEA,IAAIA,WAAW,eAAemM,IAAAA,aAAM,QAAOlS,IAAI;YAC7C,IAAI,CAACmS,QAAQ,GAAGlU,QAAQwN,OAAO;YAC/BvG,OAAOC,OAAO,CAACY,OAAO,CACpB;gBACEtG;gBACAO;gBACA/B;gBACAmU,KAAK;gBACLxC,KAAM,IAAI,CAAClH,IAAI,GAAG3C,WAAW,cAAc,IAAI,CAAC2C,IAAI,GAAGjL;YACzD,GACA,0FAA0F;YAC1F,qFAAqF;YACrF,kEAAkE;YAClE,IACAuC;QAEJ;IACF;IAEA,MAAMqS,qBACJ3K,GAAgD,EAChDlJ,QAAgB,EAChB+E,KAAqB,EACrBvD,EAAU,EACVmN,UAA2B,EAC3BmF,aAAuB,EACY;QACnC,IAAI5K,IAAI1J,SAAS,EAAE;YACjB,gCAAgC;YAChC,MAAM0J;QACR;QAEA,IAAI6K,IAAAA,yBAAY,EAAC7K,QAAQ4K,eAAe;YACtC5U,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoB5F,KAAK1H,IAAImN;YAEhD,iEAAiE;YACjE,0BAA0B;YAC1B,0CAA0C;YAC1C,4CAA4C;YAE5C,+DAA+D;YAC/DnF,qBAAqB;gBACnBvI,KAAKO;gBACL3B,QAAQ,IAAI;YACd;YAEA,kEAAkE;YAClE,8DAA8D;YAC9D,MAAMT;QACR;QAEAkM,QAAQzD,KAAK,CAACqB;QAEd,IAAI;YACF,IAAI8I;YACJ,MAAM,EAAE1P,MAAMoP,SAAS,EAAEsC,WAAW,EAAE,GACpC,MAAM,IAAI,CAACvB,cAAc,CAAC;YAE5B,MAAM3B,YAAsC;gBAC1CkB;gBACAN;gBACAsC;gBACA9K;gBACArB,OAAOqB;YACT;YAEA,IAAI,CAAC4H,UAAUkB,KAAK,EAAE;gBACpB,IAAI;oBACFlB,UAAUkB,KAAK,GAAG,MAAM,IAAI,CAACiC,eAAe,CAACvC,WAAW;wBACtDxI;wBACAlJ;wBACA+E;oBACF;gBACF,EAAE,OAAOmP,QAAQ;oBACf5I,QAAQzD,KAAK,CAAC,2CAA2CqM;oBACzDpD,UAAUkB,KAAK,GAAG,CAAC;gBACrB;YACF;YAEA,OAAOlB;QACT,EAAE,OAAOqD,cAAc;YACrB,OAAO,IAAI,CAACN,oBAAoB,CAC9BvE,IAAAA,gBAAO,EAAC6E,gBAAgBA,eAAe,qBAA4B,CAA5B,IAAI5U,MAAM4U,eAAe,KAAzB,qBAAA;uBAAA;4BAAA;8BAAA;YAA2B,IAClEnU,UACA+E,OACAvD,IACAmN,YACA;QAEJ;IACF;IAEA,MAAMoC,aAAa,KA4BlB,EAAE;QA5BgB,IAAA,EACjBrH,OAAO0K,cAAc,EACrBpU,QAAQ,EACR+E,KAAK,EACLvD,EAAE,EACFE,UAAU,EACViN,UAAU,EACVjO,MAAM,EACNuH,aAAa,EACb+I,SAAS,EACT1I,wBAAwB,EACxByE,eAAe,EACf4C,mBAAmB,EACnBgD,UAAU,EAeX,GA5BkB;QA6BjB;;;;;KAKC,GACD,IAAIjJ,QAAQ0K;QAEZ,IAAI;gBA6EAlO,cACAA,eAKEA,eAyDsBA;YA3I1B,IAAImO,eAA6C,IAAI,CAAChF,UAAU,CAAC3F,MAAM;YACvE,IAAIiF,WAAW1B,OAAO,IAAIoH,gBAAgB,IAAI,CAAC3K,KAAK,KAAKA,OAAO;gBAC9D,OAAO2K;YACT;YAEA,MAAMxK,kBAAkBJ,oBAAoB;gBAAEC;gBAAO7J,QAAQ,IAAI;YAAC;YAElE,IAAIoI,eAAe;gBACjBoM,eAAezP;YACjB;YAEA,IAAI0P,kBACFD,gBACA,CAAE,CAAA,aAAaA,YAAW,KAC1BlR,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,gBACrBqL,eACAzP;YAEN,MAAMyD,eAAe0E;YACrB,MAAMwH,sBAA2C;gBAC/CnO,UAAU,IAAI,CAACtG,UAAU,CAAC0U,WAAW,CAAC;oBACpCjM,MAAMsF,IAAAA,+BAAoB,EAAC;wBAAE7N;wBAAU+E;oBAAM;oBAC7C0P,mBAAmB;oBACnBtU,QAAQwS,aAAa,SAASjR;oBAC9BhB;gBACF;gBACAuH,eAAe;gBACfC,gBAAgB,IAAI,CAACsF,KAAK;gBAC1BrF,WAAW;gBACXJ,eAAeM,eAAe,IAAI,CAACqM,GAAG,GAAG,IAAI,CAACC,GAAG;gBACjDvM,cAAc,CAAC4I;gBACfhJ,YAAY;gBACZM;gBACAD;YACF;YAEA,IAAInC,OAKF6G,mBAAmB,CAAC4C,sBAChB,OACA,MAAM3J,sBAAsB;gBAC1BC,WAAW,IAAM6B,cAAcyM;gBAC/BpU,QAAQwS,aAAa,SAASjR;gBAC9BhB,QAAQA;gBACRb,QAAQ,IAAI;YACd,GAAGoJ,KAAK,CAAC,CAACC;gBACR,4CAA4C;gBAC5C,oDAAoD;gBACpD,oDAAoD;gBACpD,YAAY;gBACZ,IAAI6D,iBAAiB;oBACnB,OAAO;gBACT;gBACA,MAAM7D;YACR;YAEN,wDAAwD;YACxD,UAAU;YACV,IAAIhD,QAASlG,CAAAA,aAAa,aAAaA,aAAa,MAAK,GAAI;gBAC3DkG,KAAKC,MAAM,GAAGvB;YAChB;YAEA,IAAImI,iBAAiB;gBACnB,IAAI,CAAC7G,MAAM;oBACTA,OAAO;wBAAEG,MAAMgE,KAAKuI,aAAa,CAACZ,KAAK;oBAAC;gBAC1C,OAAO;oBACL9L,KAAKG,IAAI,GAAGgE,KAAKuI,aAAa,CAACZ,KAAK;gBACtC;YACF;YAEAnI;YAEA,IACE3D,CAAAA,yBAAAA,eAAAA,KAAMC,MAAM,qBAAZD,aAAcZ,IAAI,MAAK,uBACvBY,CAAAA,yBAAAA,gBAAAA,KAAMC,MAAM,qBAAZD,cAAcZ,IAAI,MAAK,qBACvB;gBACA,OAAOY,KAAKC,MAAM;YACpB;YAEA,IAAID,CAAAA,yBAAAA,gBAAAA,KAAMC,MAAM,qBAAZD,cAAcZ,IAAI,MAAK,WAAW;gBACpC,MAAMsP,gBAAgBzS,IAAAA,wCAAmB,EAAC+D,KAAKC,MAAM,CAAC1E,YAAY;gBAClE,MAAMQ,QAAQ,MAAM,IAAI,CAACnC,UAAU,CAACsE,WAAW;gBAE/C,4DAA4D;gBAC5D,yDAAyD;gBACzD,4DAA4D;gBAC5D,2CAA2C;gBAC3C,IAAI,CAAC2I,mBAAmB9K,MAAMI,QAAQ,CAACuS,gBAAgB;oBACrDlL,QAAQkL;oBACR5U,WAAWkG,KAAKC,MAAM,CAAC1E,YAAY;oBACnCsD,QAAQ;wBAAE,GAAGA,KAAK;wBAAE,GAAGmB,KAAKC,MAAM,CAACjB,QAAQ,CAACH,KAAK;oBAAC;oBAClDrD,aAAapB,IAAAA,8BAAc,EACzBmE,IAAAA,wCAAmB,EAACyB,KAAKC,MAAM,CAACjB,QAAQ,CAAClF,QAAQ,EAAE,IAAI,CAACgD,OAAO,EAC5DhD,QAAQ;oBAGb,kDAAkD;oBAClDqU,eAAe,IAAI,CAAChF,UAAU,CAAC3F,MAAM;oBACrC,IACEiF,WAAW1B,OAAO,IAClBoH,gBACA,IAAI,CAAC3K,KAAK,KAAKA,SACf,CAACzB,eACD;wBACA,4DAA4D;wBAC5D,6DAA6D;wBAC7D,gEAAgE;wBAChE,OAAO;4BAAE,GAAGoM,YAAY;4BAAE3K;wBAAM;oBAClC;gBACF;YACF;YAEA,IAAImL,IAAAA,sBAAU,EAACnL,QAAQ;gBACrBF,qBAAqB;oBAAEvI,KAAKO;oBAAI3B,QAAQ,IAAI;gBAAC;gBAC7C,OAAO,IAAIF,QAAe,KAAO;YACnC;YAEA,MAAMmR,YACJwD,mBACC,MAAM,IAAI,CAAC7B,cAAc,CAAC/I,OAAOpF,IAAI,CACpC,CAACwQ,MAAS,CAAA;oBACRpD,WAAWoD,IAAIxS,IAAI;oBACnB0R,aAAac,IAAId,WAAW;oBAC5B/B,SAAS6C,IAAIC,GAAG,CAAC9C,OAAO;oBACxBC,SAAS4C,IAAIC,GAAG,CAAC7C,OAAO;gBAC1B,CAAA;YAGJ,IAAI/O,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,cAAc;gBACzC,MAAM,EAAEgM,kBAAkB,EAAE,GAAG/J,QAAQ;gBACvC,IAAI,CAAC+J,mBAAmBlE,UAAUY,SAAS,GAAG;oBAC5C,MAAM,qBAEL,CAFK,IAAInS,MACR,AAAC,2DAAwDS,WAAS,MAD9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAMiV,oBAAoB/O,yBAAAA,iBAAAA,KAAMtD,QAAQ,qBAAdsD,eAAgB3C,OAAO,CAACC,GAAG,CAAC;YAEtD,MAAM0R,kBAAkBpE,UAAUmB,OAAO,IAAInB,UAAUoB,OAAO;YAE9D,yDAAyD;YACzD,4CAA4C;YAC5C,IAAI+C,sBAAqB/O,wBAAAA,KAAME,QAAQ,GAAE;gBACvC,OAAO,IAAI,CAACuO,GAAG,CAACzO,KAAKE,QAAQ,CAAC;YAChC;YAEA,MAAM,EAAE4L,KAAK,EAAEzL,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC4O,QAAQ,CAAC;gBAC9C,IAAID,iBAAiB;oBACnB,IAAIhP,CAAAA,wBAAAA,KAAMG,IAAI,KAAI,CAAC4O,mBAAmB;wBACpC,OAAO;4BAAE1O,UAAUL,KAAKK,QAAQ;4BAAEyL,OAAO9L,KAAKG,IAAI;wBAAC;oBACrD;oBAEA,MAAMD,WAAWF,CAAAA,wBAAAA,KAAME,QAAQ,IAC3BF,KAAKE,QAAQ,GACb,IAAI,CAACtG,UAAU,CAAC0U,WAAW,CAAC;wBAC1BjM,MAAMsF,IAAAA,+BAAoB,EAAC;4BAAE7N;4BAAU+E;wBAAM;wBAC7C5E,QAAQuB;wBACRhB;oBACF;oBAEJ,MAAM0U,UAAU,MAAMtN,cAAc;wBAClC1B;wBACA8B,gBAAgB,IAAI,CAACsF,KAAK;wBAC1BrF,WAAW;wBACXJ,eAAekN,oBAAoB,CAAC,IAAI,IAAI,CAACN,GAAG;wBAChDvM,cAAc,CAAC4I;wBACfhJ,YAAY;wBACZM;oBACF;oBAEA,OAAO;wBACL/B,UAAU6O,QAAQ7O,QAAQ;wBAC1ByL,OAAOoD,QAAQ/O,IAAI,IAAI,CAAC;oBAC1B;gBACF;gBAEA,OAAO;oBACL9C,SAAS,CAAC;oBACVyO,OAAO,MAAM,IAAI,CAACiC,eAAe,CAC/BnD,UAAUY,SAAS,EACnB,qDAAqD;oBACrD;wBACE1R;wBACA+E;wBACA5E,QAAQqB;wBACRd;wBACAsC,SAAS,IAAI,CAACA,OAAO;wBACrByC,eAAe,IAAI,CAACA,aAAa;oBACnC;gBAEJ;YACF;YAEA,mDAAmD;YACnD,6CAA6C;YAC7C,uCAAuC;YACvC,IAAIqL,UAAUoB,OAAO,IAAIqC,oBAAoBnO,QAAQ,IAAIG,UAAU;gBACjE,OAAO,IAAI,CAACoO,GAAG,CAACpO,SAAS;YAC3B;YAEA,+CAA+C;YAC/C,6DAA6D;YAC7D,IACE,CAAC,IAAI,CAACyK,SAAS,IACfF,UAAUmB,OAAO,IACjB9O,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,iBACzB,CAAC+D,iBACD;gBACAjF,cACEzI,OAAOC,MAAM,CAAC,CAAC,GAAGiV,qBAAqB;oBACrClM,cAAc;oBACdD,cAAc;oBACdL,eAAe,IAAI,CAAC2M,GAAG;gBACzB,IACAzL,KAAK,CAAC,KAAO;YACjB;YAEA+I,MAAMG,SAAS,GAAG9S,OAAOC,MAAM,CAAC,CAAC,GAAG0S,MAAMG,SAAS;YACnDrB,UAAUkB,KAAK,GAAGA;YAClBlB,UAAUpH,KAAK,GAAGA;YAClBoH,UAAU/L,KAAK,GAAGA;YAClB+L,UAAUpP,UAAU,GAAGA;YACvB,IAAI,CAAC2N,UAAU,CAAC3F,MAAM,GAAGoH;YAEzB,OAAOA;QACT,EAAE,OAAO5H,KAAK;YACZ,OAAO,IAAI,CAAC2K,oBAAoB,CAC9BwB,IAAAA,uBAAc,EAACnM,MACflJ,UACA+E,OACAvD,IACAmN;QAEJ;IACF;IAEQS,IACN/B,KAAwB,EACxBnH,IAAsB,EACtB8M,WAA4C,EAC7B;QACf,IAAI,CAAC3F,KAAK,GAAGA;QAEb,OAAO,IAAI,CAACiI,GAAG,CACbpP,MACA,IAAI,CAACmJ,UAAU,CAAC,QAAQ,CAACqC,SAAS,EAClCsB;IAEJ;IAEA;;;GAGC,GACDuC,eAAeC,EAA0B,EAAE;QACzC,IAAI,CAACC,IAAI,GAAGD;IACd;IAEAvG,gBAAgBzN,EAAU,EAAW;QACnC,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE,OAAO;QACzB,MAAM,CAACuV,cAAcC,QAAQ,GAAG,IAAI,CAACxV,MAAM,CAACsM,KAAK,CAAC,KAAK;QACvD,MAAM,CAACmJ,cAAcC,QAAQ,GAAGrU,GAAGiL,KAAK,CAAC,KAAK;QAE9C,yEAAyE;QACzE,IAAIoJ,WAAWH,iBAAiBE,gBAAgBD,YAAYE,SAAS;YACnE,OAAO;QACT;QAEA,0DAA0D;QAC1D,IAAIH,iBAAiBE,cAAc;YACjC,OAAO;QACT;QAEA,yDAAyD;QACzD,uDAAuD;QACvD,2DAA2D;QAC3D,mCAAmC;QACnC,OAAOD,YAAYE;IACrB;IAEA1G,aAAa3N,EAAU,EAAQ;QAC7B,MAAM,GAAGoE,OAAO,EAAE,CAAC,GAAGpE,GAAGiL,KAAK,CAAC,KAAK;QAEpCqJ,IAAAA,sCAAkB,EAChB;YACE,gEAAgE;YAChE,qBAAqB;YACrB,IAAIlQ,SAAS,MAAMA,SAAS,OAAO;gBACjCc,OAAOqP,QAAQ,CAAC,GAAG;gBACnB;YACF;YAEA,8CAA8C;YAC9C,MAAMC,UAAUC,mBAAmBrQ;YACnC,+CAA+C;YAC/C,MAAMsQ,OAAO5C,SAAS6C,cAAc,CAACH;YACrC,IAAIE,MAAM;gBACRA,KAAKE,cAAc;gBACnB;YACF;YACA,kEAAkE;YAClE,qBAAqB;YACrB,MAAMC,SAAS/C,SAASgD,iBAAiB,CAACN,QAAQ,CAAC,EAAE;YACrD,IAAIK,QAAQ;gBACVA,OAAOD,cAAc;YACvB;QACF,GACA;YACEG,gBAAgB,IAAI,CAACtH,eAAe,CAACzN;QACvC;IAEJ;IAEAgO,SAASrP,MAAc,EAAW;QAChC,OAAO,IAAI,CAACA,MAAM,KAAKA;IACzB;IAEA;;;;;GAKC,GACD,MAAMqW,SACJvV,GAAW,EACXd,MAAoB,EACpBV,OAA6B,EACd;QAFfU,IAAAA,mBAAAA,SAAiBc;QACjBxB,IAAAA,oBAAAA,UAA2B,CAAC;QAE5B,2FAA2F;QAC3F,IAAI0D,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,OAAOtC,WAAW,eAAe+P,IAAAA,YAAK,EAAC/P,OAAOgQ,SAAS,CAACC,SAAS,GAAG;YACtE,kFAAkF;YAClF,8EAA8E;YAC9E,cAAc;YACd;QACF;QACA,IAAIpH,SAASzL,IAAAA,kCAAgB,EAAC7C;QAC9B,MAAM2V,cAAcrH,OAAOvP,QAAQ;QAEnC,IAAI,EAAEA,QAAQ,EAAE+E,KAAK,EAAE,GAAGwK;QAC1B,MAAMsH,mBAAmB7W;QAEzB,IAAImD,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;YACnC,IAAIjO,QAAQiB,MAAM,KAAK,OAAO;gBAC5BV,WAAWyE,IAAAA,wCAAmB,EAAEzE,UAAU,IAAI,CAACgD,OAAO,EAAEhD,QAAQ;gBAChEuP,OAAOvP,QAAQ,GAAGA;gBAClBiB,MAAM4M,IAAAA,+BAAoB,EAAC0B;gBAE3B,IAAIrK,WAAWpB,IAAAA,kCAAgB,EAAC3D;gBAChC,MAAMwN,mBAAmBlJ,IAAAA,wCAAmB,EAC1CS,SAASlF,QAAQ,EACjB,IAAI,CAACgD,OAAO;gBAEdkC,SAASlF,QAAQ,GAAG2N,iBAAiB3N,QAAQ;gBAC7CP,QAAQiB,MAAM,GAAGiN,iBAAiBC,cAAc,IAAI,IAAI,CAACnI,aAAa;gBACtEtF,SAAS0N,IAAAA,+BAAoB,EAAC3I;YAChC;QACF;QAEA,MAAMjD,QAAQ,MAAM,IAAI,CAACnC,UAAU,CAACsE,WAAW;QAC/C,IAAI1C,aAAavB;QAEjB,MAAMO,SACJ,OAAOjB,QAAQiB,MAAM,KAAK,cACtBjB,QAAQiB,MAAM,IAAIkE,YAClB,IAAI,CAAClE,MAAM;QAEjB,MAAMkP,oBAAoB,MAAMzQ,kBAAkB;YAChDgB,QAAQA;YACRO,QAAQA;YACRb,QAAQ,IAAI;QACd;QAEA,IAAIsD,QAAQC,GAAG,CAACuB,mBAAmB,IAAIxE,OAAOiB,UAAU,CAAC,MAAM;YAC7D,IAAIoD;YACF,CAAA,EAAED,YAAYC,QAAQ,EAAE,GAAG,MAAMH,IAAAA,mCAAsB,GAAC;YAE1D,MAAMwL,iBAAiB/K,IAAAA,wBAAe,EACpCtE,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAACN,QAAQ,IAAI,CAACO,MAAM,GAAG,OAC5CuB,OACAuC,UACA+K,OAAOxK,KAAK,EACZ,CAAC+K,IAAc9N,oBAAoB8N,GAAG7N,QACtC,IAAI,CAACe,OAAO;YAGd,IAAI6M,eAAeE,YAAY,EAAE;gBAC/B;YACF;YAEA,IAAI,CAACH,mBAAmB;gBACtBlO,aAAaqN,IAAAA,0BAAY,EACvBzO,IAAAA,8BAAc,EAACuP,eAAe1P,MAAM,GACpC,IAAI,CAACO,MAAM;YAEf;YAEA,IAAImP,eAAe5K,WAAW,IAAI4K,eAAepO,YAAY,EAAE;gBAC7D,gEAAgE;gBAChE,4CAA4C;gBAC5CzB,WAAW6P,eAAepO,YAAY;gBACtC8N,OAAOvP,QAAQ,GAAGA;gBAElB,IAAI,CAAC4P,mBAAmB;oBACtB3O,MAAM4M,IAAAA,+BAAoB,EAAC0B;gBAC7B;YACF;QACF;QACAA,OAAOvP,QAAQ,GAAGgC,oBAAoBuN,OAAOvP,QAAQ,EAAEiC;QAEvD,IAAIM,IAAAA,yBAAc,EAACgN,OAAOvP,QAAQ,GAAG;YACnCA,WAAWuP,OAAOvP,QAAQ;YAC1BuP,OAAOvP,QAAQ,GAAGA;YAClBX,OAAOC,MAAM,CACXyF,OACAM,IAAAA,6BAAe,EAAC7C,IAAAA,yBAAa,EAAC+M,OAAOvP,QAAQ,GAC3CE,IAAAA,oBAAS,EAACC,QAAQH,QAAQ,KACvB,CAAC;YAGR,IAAI,CAAC4P,mBAAmB;gBACtB3O,MAAM4M,IAAAA,+BAAoB,EAAC0B;YAC7B;QACF;QAEA,MAAMrJ,OACJ/C,QAAQC,GAAG,CAAC0T,0BAA0B,KAAK,WACvC,OACA,MAAM9Q,sBAAsB;YAC1BC,WAAW,IACT6B,cAAc;oBACZ1B,UAAU,IAAI,CAACtG,UAAU,CAAC0U,WAAW,CAAC;wBACpCjM,MAAMsF,IAAAA,+BAAoB,EAAC;4BACzB7N,UAAU6W;4BACV9R;wBACF;wBACA0P,mBAAmB;wBACnBtU,QAAQuB;wBACRhB;oBACF;oBACAuH,eAAe;oBACfC,gBAAgB;oBAChBC,WAAW;oBACXJ,eAAe,IAAI,CAAC4M,GAAG;oBACvBvM,cAAc,CAAC,IAAI,CAAC4I,SAAS;oBAC7BhJ,YAAY;gBACd;YACF7H,QAAQA;YACRO,QAAQA;YACRb,QAAQ,IAAI;QACd;QAEN;;;KAGC,GACD,IAAIqG,CAAAA,wBAAAA,KAAMC,MAAM,CAACb,IAAI,MAAK,WAAW;YACnCiK,OAAOvP,QAAQ,GAAGkG,KAAKC,MAAM,CAAC1E,YAAY;YAC1CzB,WAAWkG,KAAKC,MAAM,CAAC1E,YAAY;YACnCsD,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGmB,KAAKC,MAAM,CAACjB,QAAQ,CAACH,KAAK;YAAC;YAClDrD,aAAawE,KAAKC,MAAM,CAACjB,QAAQ,CAAClF,QAAQ;YAC1CiB,MAAM4M,IAAAA,+BAAoB,EAAC0B;QAC7B;QAEA;;;KAGC,GACD,IAAIrJ,CAAAA,wBAAAA,KAAMC,MAAM,CAACb,IAAI,MAAK,qBAAqB;YAC7C;QACF;QAEA,MAAMoE,QAAQvH,IAAAA,wCAAmB,EAACnC;QAElC,IAAI,MAAM,IAAI,CAAC2K,IAAI,CAACxK,QAAQuB,YAAYjC,QAAQiB,MAAM,EAAE,OAAO;YAC7D,IAAI,CAAC2O,UAAU,CAACuH,YAAY,GAAG;gBAAElH,aAAa;YAAK;QACrD;QAEA,MAAM/P,QAAQwE,GAAG,CAAC;YAChB,IAAI,CAACrE,UAAU,CAACiX,MAAM,CAACrN,OAAOpF,IAAI,CAAC,CAAC0S;gBAClC,OAAOA,QACHlP,cAAc;oBACZ1B,UAAUF,CAAAA,wBAAAA,KAAMG,IAAI,IAChBH,wBAAAA,KAAME,QAAQ,GACd,IAAI,CAACtG,UAAU,CAAC0U,WAAW,CAAC;wBAC1BjM,MAAMtH;wBACNd,QAAQuB;wBACRhB,QAAQA;oBACV;oBACJwH,gBAAgB;oBAChBC,WAAW;oBACXJ,eAAe,IAAI,CAAC4M,GAAG;oBACvBvM,cAAc,CAAC,IAAI,CAAC4I,SAAS;oBAC7BhJ,YAAY;oBACZM,0BACE7I,QAAQ6I,wBAAwB,IAC/B7I,QAAQwX,QAAQ,IACf,CAAC,CAAC9T,QAAQC,GAAG,CAAC8T,8BAA8B;gBAClD,GACG5S,IAAI,CAAC,IAAM,OACX2E,KAAK,CAAC,IAAM,SACf;YACN;YACA,IAAI,CAACnJ,UAAU,CAACL,QAAQwX,QAAQ,GAAG,aAAa,WAAW,CAACvN;SAC7D;IACH;IAEA,MAAM+I,eAAe/I,KAAa,EAAE;QAClC,MAAMG,kBAAkBJ,oBAAoB;YAAEC;YAAO7J,QAAQ,IAAI;QAAC;QAElE,IAAI;YACF,MAAMsX,kBAAkB,MAAM,IAAI,CAACrX,UAAU,CAACsX,QAAQ,CAAC1N;YACvDG;YAEA,OAAOsN;QACT,EAAE,OAAOjO,KAAK;YACZW;YACA,MAAMX;QACR;IACF;IAEAiM,SAAYkC,EAAoB,EAAc;QAC5C,IAAI7X,YAAY;QAChB,MAAMmK,SAAS;YACbnK,YAAY;QACd;QACA,IAAI,CAACoK,GAAG,GAAGD;QACX,OAAO0N,KAAK/S,IAAI,CAAC,CAAC4B;YAChB,IAAIyD,WAAW,IAAI,CAACC,GAAG,EAAE;gBACvB,IAAI,CAACA,GAAG,GAAG;YACb;YAEA,IAAIpK,WAAW;gBACb,MAAM0J,MAAW,qBAA4C,CAA5C,IAAI3J,MAAM,oCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA2C;gBAC5D2J,IAAI1J,SAAS,GAAG;gBAChB,MAAM0J;YACR;YAEA,OAAOhD;QACT;IACF;IAEA+N,gBACEvC,SAAwB,EACxB4F,GAAoB,EACU;QAC9B,MAAM,EAAE5F,WAAW6F,GAAG,EAAE,GAAG,IAAI,CAAClI,UAAU,CAAC,QAAQ;QACnD,MAAMmI,UAAU,IAAI,CAACC,QAAQ,CAACF;QAC9BD,IAAIE,OAAO,GAAGA;QACd,OAAOE,IAAAA,0BAAmB,EAAyBH,KAAK;YACtDC;YACA9F;YACA7R,QAAQ,IAAI;YACZyX;QACF;IACF;IAEA,IAAI5N,QAAgB;QAClB,OAAO,IAAI,CAAC2D,KAAK,CAAC3D,KAAK;IACzB;IAEA,IAAI1J,WAAmB;QACrB,OAAO,IAAI,CAACqN,KAAK,CAACrN,QAAQ;IAC5B;IAEA,IAAI+E,QAAwB;QAC1B,OAAO,IAAI,CAACsI,KAAK,CAACtI,KAAK;IACzB;IAEA,IAAI5E,SAAiB;QACnB,OAAO,IAAI,CAACkN,KAAK,CAAClN,MAAM;IAC1B;IAEA,IAAIO,SAA6B;QAC/B,OAAO,IAAI,CAAC2M,KAAK,CAAC3M,MAAM;IAC1B;IAEA,IAAIuQ,aAAsB;QACxB,OAAO,IAAI,CAAC5D,KAAK,CAAC4D,UAAU;IAC9B;IAEA,IAAID,YAAqB;QACvB,OAAO,IAAI,CAAC3D,KAAK,CAAC2D,SAAS;IAC7B;IAh1DA2G,YACE3X,QAAgB,EAChB+E,KAAqB,EACrBvD,EAAU,EACV,EACEoW,YAAY,EACZ9X,UAAU,EACVyX,GAAG,EACHM,OAAO,EACPnG,SAAS,EACTxI,GAAG,EACH4O,YAAY,EACZ7G,UAAU,EACVvQ,MAAM,EACNsC,OAAO,EACPyC,aAAa,EACbwI,aAAa,EACb+C,SAAS,EAeV,CACD;QAzEF,yCAAyC;aACzC2D,MAAqB,CAAC;QACtB,0CAA0C;aAC1CD,MAAqB,CAAC;aAgBtBqD,uBAAuB;aAiBf7N,OAAejL;aA+JvB+Y,aAAa,CAAC3E;YACZ,MAAM,EAAE0E,oBAAoB,EAAE,GAAG,IAAI;YACrC,IAAI,CAACA,oBAAoB,GAAG;YAE5B,MAAM1K,QAAQgG,EAAEhG,KAAK;YAErB,IAAI,CAACA,OAAO;gBACV,6CAA6C;gBAC7C,sDAAsD;gBACtD,kCAAkC;gBAClC,EAAE;gBACF,oEAAoE;gBACpE,4BAA4B;gBAC5B,4DAA4D;gBAC5D,kFAAkF;gBAClF,gDAAgD;gBAChD,MAAM,EAAErN,QAAQ,EAAE+E,KAAK,EAAE,GAAG,IAAI;gBAChC,IAAI,CAACmK,WAAW,CACd,gBACArB,IAAAA,+BAAoB,EAAC;oBAAE7N,UAAUQ,IAAAA,wBAAW,EAACR;oBAAW+E;gBAAM,IAC9D2O,IAAAA,aAAM;gBAER;YACF;YAEA,kFAAkF;YAClF,IAAIrG,MAAM4K,IAAI,EAAE;gBACdvR,OAAO+B,QAAQ,CAACqB,MAAM;gBACtB;YACF;YAEA,IAAI,CAACuD,MAAMuG,GAAG,EAAE;gBACd;YACF;YAEA,yDAAyD;YACzD,IACEmE,wBACA,IAAI,CAACrX,MAAM,KAAK2M,MAAM5N,OAAO,CAACiB,MAAM,IACpC2M,MAAM7L,EAAE,KAAK,IAAI,CAACrB,MAAM,EACxB;gBACA;YACF;YAEA,IAAI0M;YACJ,MAAM,EAAE5L,GAAG,EAAEO,EAAE,EAAE/B,OAAO,EAAE2R,GAAG,EAAE,GAAG/D;YAClC,IAAIlK,QAAQC,GAAG,CAACqD,yBAAyB,EAAE;gBACzC,IAAID,yBAAyB;oBAC3B,IAAI,IAAI,CAAC0D,IAAI,KAAKkH,KAAK;wBACrB,oCAAoC;wBACpC,IAAI;4BACFvK,eAAeC,OAAO,CACpB,mBAAmB,IAAI,CAACoD,IAAI,EAC5BvC,KAAKwC,SAAS,CAAC;gCAAEC,GAAGC,KAAKC,WAAW;gCAAEC,GAAGF,KAAKG,WAAW;4BAAC;wBAE9D,EAAE,UAAM,CAAC;wBAET,+BAA+B;wBAC/B,IAAI;4BACF,MAAM5D,IAAIC,eAAeqR,OAAO,CAAC,mBAAmB9G;4BACpDvE,eAAelF,KAAKC,KAAK,CAAChB;wBAC5B,EAAE,UAAM;4BACNiG,eAAe;gCAAEzC,GAAG;gCAAGG,GAAG;4BAAE;wBAC9B;oBACF;gBACF;YACF;YACA,IAAI,CAACL,IAAI,GAAGkH;YAEZ,MAAM,EAAEpR,QAAQ,EAAE,GAAG8D,IAAAA,kCAAgB,EAAC7C;YAEtC,gDAAgD;YAChD,yDAAyD;YACzD,IACE,IAAI,CAACuM,KAAK,IACVhM,OAAOhB,IAAAA,wBAAW,EAAC,IAAI,CAACL,MAAM,KAC9BH,aAAaQ,IAAAA,wBAAW,EAAC,IAAI,CAACR,QAAQ,GACtC;gBACA;YACF;YAEA,uDAAuD;YACvD,wDAAwD;YACxD,IAAI,IAAI,CAACyV,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACpI,QAAQ;gBAClC;YACF;YAEA,IAAI,CAAC5C,MAAM,CACT,gBACAxJ,KACAO,IACAnC,OAAOC,MAAM,CAA2C,CAAC,GAAGG,SAAS;gBACnEwN,SAASxN,QAAQwN,OAAO,IAAI,IAAI,CAAC0G,QAAQ;gBACzCjT,QAAQjB,QAAQiB,MAAM,IAAI,IAAI,CAAC+E,aAAa;gBAC5C,iDAAiD;gBACjDuH,IAAI;YACN,IACAH;QAEJ;QA5NE,uCAAuC;QACvC,MAAMnD,QAAQvH,IAAAA,wCAAmB,EAACnC;QAElC,6CAA6C;QAC7C,IAAI,CAACqP,UAAU,GAAG,CAAC;QACnB,oDAAoD;QACpD,wDAAwD;QACxD,kCAAkC;QAClC,IAAIrP,aAAa,WAAW;YAC1B,IAAI,CAACqP,UAAU,CAAC3F,MAAM,GAAG;gBACvBgI;gBACAyG,SAAS;gBACTnG,OAAO4F;gBACP1O;gBACA+I,SAAS2F,gBAAgBA,aAAa3F,OAAO;gBAC7CC,SAAS0F,gBAAgBA,aAAa1F,OAAO;YAC/C;QACF;QAEA,IAAI,CAAC7C,UAAU,CAAC,QAAQ,GAAG;YACzBqC,WAAW6F;YACXvD,aAAa,EAEZ;QACH;QAEA,4CAA4C;QAC5C,gFAAgF;QAChF,IAAI,CAACnF,MAAM,GAAG3P,OAAO2P,MAAM;QAE3B,IAAI,CAAC/O,UAAU,GAAGA;QAClB,8DAA8D;QAC9D,kDAAkD;QAClD,MAAMsY,oBACJ7V,IAAAA,yBAAc,EAACvC,aAAaqK,KAAKuI,aAAa,CAACyF,UAAU;QAE3D,IAAI,CAACvV,QAAQ,GAAGK,QAAQC,GAAG,CAACkV,sBAAsB,IAAI;QACtD,IAAI,CAAChD,GAAG,GAAGwC;QACX,IAAI,CAAClO,GAAG,GAAG;QACX,IAAI,CAAC6N,QAAQ,GAAGI;QAChB,6DAA6D;QAC7D,0BAA0B;QAC1B,IAAI,CAACrK,KAAK,GAAG;QACb,IAAI,CAACU,cAAc,GAAG;QACtB,IAAI,CAACX,OAAO,GAAG,CAAC,CACdlD,CAAAA,KAAKuI,aAAa,CAAC2F,IAAI,IACvBlO,KAAKuI,aAAa,CAAC4F,GAAG,IACtBnO,KAAKuI,aAAa,CAAC6F,qBAAqB,IACvCpO,KAAKuI,aAAa,CAAC8F,MAAM,IAAI,CAACrO,KAAKuI,aAAa,CAAC+F,GAAG,IACpD,CAACP,qBACA,CAAC/N,KAAK5B,QAAQ,CAACmQ,MAAM,IACrB,CAACzV,QAAQC,GAAG,CAACuB,mBAAmB;QAGpC,IAAIxB,QAAQC,GAAG,CAACsK,mBAAmB,EAAE;YACnC,IAAI,CAAC1K,OAAO,GAAGA;YACf,IAAI,CAACyC,aAAa,GAAGA;YACrB,IAAI,CAACwI,aAAa,GAAGA;YACrB,IAAI,CAACC,cAAc,GAAG,CAAC,CAACF,IAAAA,sCAAkB,EACxCC,eACA5D,KAAK5B,QAAQ,CAAC0F,QAAQ;QAE1B;QAEA,IAAI,CAACd,KAAK,GAAG;YACX3D;YACA1J;YACA+E;YACA5E,QAAQiY,oBAAoBpY,WAAWwB;YACvCwP,WAAW,CAAC,CAACA;YACbtQ,QAAQyC,QAAQC,GAAG,CAACsK,mBAAmB,GAAGhN,SAASkE;YACnDqM;QACF;QAEA,IAAI,CAAC4H,gCAAgC,GAAGlZ,QAAQC,OAAO,CAAC;QAExD,IAAI,OAAO8G,WAAW,aAAa;YACjC,kEAAkE;YAClE,4CAA4C;YAC5C,IAAI,CAAClF,GAAGJ,UAAU,CAAC,OAAO;gBACxB,2DAA2D;gBAC3D,4DAA4D;gBAC5D,MAAM3B,UAA6B;oBAAEiB;gBAAO;gBAC5C,MAAMP,SAASuT,IAAAA,aAAM;gBAErB,IAAI,CAACmF,gCAAgC,GAAG1Z,kBAAkB;oBACxDU,QAAQ,IAAI;oBACZa;oBACAP;gBACF,GAAGmE,IAAI,CAAC,CAACc;oBACP,kEAAkE;oBAClE,sDAAsD;;oBACpD3F,QAAgB0N,kBAAkB,GAAG3L,OAAOxB;oBAE9C,IAAI,CAACkP,WAAW,CACd,gBACA9J,UACIjF,SACA0N,IAAAA,+BAAoB,EAAC;wBACnB7N,UAAUQ,IAAAA,wBAAW,EAACR;wBACtB+E;oBACF,IACJ5E,QACAV;oBAEF,OAAO2F;gBACT;YACF;YAEAsB,OAAOoS,gBAAgB,CAAC,YAAY,IAAI,CAACd,UAAU;YAEnD,2DAA2D;YAC3D,mDAAmD;YACnD,IAAI7U,QAAQC,GAAG,CAACqD,yBAAyB,EAAE;gBACzC,IAAID,yBAAyB;oBAC3BE,OAAOC,OAAO,CAACoS,iBAAiB,GAAG;gBACrC;YACF;QACF;IACF;AAwrDF;AAh4DqB7Z,OA6CZ2P,SAAmCmK,IAAAA,aAAI"}