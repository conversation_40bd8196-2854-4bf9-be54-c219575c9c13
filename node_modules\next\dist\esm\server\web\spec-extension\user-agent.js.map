{"version": 3, "sources": ["../../../../src/server/web/spec-extension/user-agent.ts"], "sourcesContent": ["import parseua from 'next/dist/compiled/ua-parser-js'\n\ninterface UserAgent {\n  isBot: boolean\n  ua: string\n  browser: {\n    name?: string\n    version?: string\n    major?: string\n  }\n  device: {\n    model?: string\n    type?: string\n    vendor?: string\n  }\n  engine: {\n    name?: string\n    version?: string\n  }\n  os: {\n    name?: string\n    version?: string\n  }\n  cpu: {\n    architecture?: string\n  }\n}\n\nexport function isBot(input: string): boolean {\n  return /Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(\n    input\n  )\n}\n\nexport function userAgentFromString(input: string | undefined): UserAgent {\n  return {\n    ...parseua(input),\n    isBot: input === undefined ? false : isBot(input),\n  }\n}\n\nexport function userAgent({ headers }: { headers: Headers }): UserAgent {\n  return userAgentFromString(headers.get('user-agent') || undefined)\n}\n"], "names": ["parseua", "isBot", "input", "test", "userAgentFromString", "undefined", "userAgent", "headers", "get"], "mappings": "AAAA,OAAOA,aAAa,kCAAiC;AA4BrD,OAAO,SAASC,MAAMC,KAAa;IACjC,OAAO,0WAA0WC,IAAI,CACnXD;AAEJ;AAEA,OAAO,SAASE,oBAAoBF,KAAyB;IAC3D,OAAO;QACL,GAAGF,QAAQE,MAAM;QACjBD,OAAOC,UAAUG,YAAY,QAAQJ,MAAMC;IAC7C;AACF;AAEA,OAAO,SAASI,UAAU,EAAEC,OAAO,EAAwB;IACzD,OAAOH,oBAAoBG,QAAQC,GAAG,CAAC,iBAAiBH;AAC1D"}