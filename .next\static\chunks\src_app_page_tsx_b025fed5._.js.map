{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { APP_NAME } from '@/constants'\nimport { useAppSelector } from '@/lib/store'\n\nexport default function Home() {\n  const { isAuthenticated } = useAppSelector((state) => state.auth)\n\n  return (\n    <div className=\"bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Welcome to <span className=\"text-blue-600\">{APP_NAME}</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            A professional e-commerce platform built with Next.js 15, TypeScript, Tailwind CSS,\n            Redux Toolkit, and Supabase. Experience modern web development at its finest.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\">Get Started</Button>\n            <Button variant=\"outline\" size=\"lg\">Learn More</Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 px-4 sm:px-6 lg:px-8 bg-white\">\n        <div className=\"max-w-7xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            Tech Stack Features\n          </h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <FeatureCard\n              title=\"Next.js 15\"\n              description=\"Latest version with App Router, Server Components, and Turbopack for blazing fast development.\"\n              icon=\"⚡\"\n            />\n            <FeatureCard\n              title=\"TypeScript\"\n              description=\"Full type safety across the entire application for better developer experience and fewer bugs.\"\n              icon=\"🔒\"\n            />\n            <FeatureCard\n              title=\"Tailwind CSS\"\n              description=\"Utility-first CSS framework for rapid UI development with consistent design.\"\n              icon=\"🎨\"\n            />\n            <FeatureCard\n              title=\"Redux Toolkit\"\n              description=\"Predictable state management with modern Redux patterns and excellent DevTools.\"\n              icon=\"🔄\"\n            />\n            <FeatureCard\n              title=\"Supabase\"\n              description=\"Open source Firebase alternative with PostgreSQL database and real-time features.\"\n              icon=\"🗄️\"\n            />\n            <FeatureCard\n              title=\"Professional Setup\"\n              description=\"ESLint, Prettier, and VS Code configuration for optimal development workflow.\"\n              icon=\"⚙️\"\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Status Section */}\n      <section className=\"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Installation Status</h2>\n          <div className=\"bg-white rounded-lg shadow-lg p-8\">\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <StatusItem label=\"Next.js 15 + TypeScript\" status=\"✅\" />\n              <StatusItem label=\"Tailwind CSS v4\" status=\"✅\" />\n              <StatusItem label=\"Redux Toolkit\" status=\"✅\" />\n              <StatusItem label=\"Supabase Integration\" status=\"✅\" />\n              <StatusItem label=\"ESLint + Prettier\" status=\"✅\" />\n              <StatusItem label=\"Professional Structure\" status=\"✅\" />\n            </div>\n            <p className=\"mt-6 text-gray-600\">\n              Authentication Status: {isAuthenticated ? '🟢 Logged In' : '🔴 Not Logged In'}\n            </p>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n\ninterface FeatureCardProps {\n  title: string\n  description: string\n  icon: string\n}\n\nfunction FeatureCard({ title, description, icon }: FeatureCardProps) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\">\n      <div className=\"text-3xl mb-4\">{icon}</div>\n      <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600\">{description}</p>\n    </div>\n  )\n}\n\ninterface StatusItemProps {\n  label: string\n  status: string\n}\n\nfunction StatusItem({ label, status }: StatusItemProps) {\n  return (\n    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n      <span className=\"font-medium text-gray-900\">{label}</span>\n      <span className=\"text-lg\">{status}</span>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;+BAAE,CAAC,QAAU,MAAM,IAAI;;IAEhE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAoD;8CACrD,6LAAC;oCAAK,WAAU;8CAAiB,4HAAA,CAAA,WAAQ;;;;;;;;;;;;sCAEtD,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;8CAAK;;;;;;8CAClB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAK;;;;;;8CAEP,6LAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAK;;;;;;8CAEP,6LAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAK;;;;;;8CAEP,6LAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAK;;;;;;8CAEP,6LAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAK;;;;;;8CAEP,6LAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAW,OAAM;4CAA0B,QAAO;;;;;;sDACnD,6LAAC;4CAAW,OAAM;4CAAkB,QAAO;;;;;;sDAC3C,6LAAC;4CAAW,OAAM;4CAAgB,QAAO;;;;;;sDACzC,6LAAC;4CAAW,OAAM;4CAAuB,QAAO;;;;;;sDAChD,6LAAC;4CAAW,OAAM;4CAAoB,QAAO;;;;;;sDAC7C,6LAAC;4CAAW,OAAM;4CAAyB,QAAO;;;;;;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;;wCAAqB;wCACR,kBAAkB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE;GApFwB;;QACM,sHAAA,CAAA,iBAAc;;;KADpB;AA4FxB,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAoB;IACjE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAiB;;;;;;0BAChC,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAAiB;;;;;;;;;;;;AAGpC;MARS;AAeT,SAAS,WAAW,EAAE,KAAK,EAAE,MAAM,EAAmB;IACpD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAA6B;;;;;;0BAC7C,6LAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC;MAPS", "debugId": null}}]}