{"version": 3, "sources": ["../../../../src/server/web/sandbox/resource-managers.ts"], "sourcesContent": ["abstract class ResourceManager<T, Args> {\n  private resources: T[] = []\n\n  abstract create(resourceArgs: Args): T\n  abstract destroy(resource: T): void\n\n  add(resourceArgs: Args) {\n    const resource = this.create(resourceArgs)\n    this.resources.push(resource)\n    return resource\n  }\n\n  remove(resource: T) {\n    this.resources = this.resources.filter((r) => r !== resource)\n    this.destroy(resource)\n  }\n\n  removeAll() {\n    this.resources.forEach(this.destroy)\n    this.resources = []\n  }\n}\n\nclass IntervalsManager extends ResourceManager<\n  number,\n  Parameters<typeof setInterval>\n> {\n  create(args: Parameters<typeof setInterval>) {\n    // TODO: use the edge runtime provided `setInterval` instead\n    return webSetIntervalPolyfill(...args)\n  }\n\n  destroy(interval: number) {\n    clearInterval(interval)\n  }\n}\n\nclass TimeoutsManager extends ResourceManager<\n  number,\n  Parameters<typeof setTimeout>\n> {\n  create(args: Parameters<typeof setTimeout>) {\n    // TODO: use the edge runtime provided `setTimeout` instead\n    return webSetTimeoutPolyfill(...args)\n  }\n\n  destroy(timeout: number) {\n    clearTimeout(timeout)\n  }\n}\n\nfunction webSetIntervalPolyfill<TArgs extends any[]>(\n  callback: (...args: TArgs) => void,\n  ms?: number,\n  ...args: TArgs\n): number {\n  return setInterval(() => {\n    // node's `setInterval` sets `this` to the `Timeout` instance it returned,\n    // but web `setInterval` always sets `this` to `window`\n    // see: https://developer.mozilla.org/en-US/docs/Web/API/Window/setInterval#the_this_problem\n    return callback.apply(globalThis, args)\n  }, ms)[Symbol.toPrimitive]()\n}\n\nfunction webSetTimeoutPolyfill<TArgs extends any[]>(\n  callback: (...args: TArgs) => void,\n  ms?: number,\n  ...args: TArgs\n): number {\n  const wrappedCallback = () => {\n    try {\n      // node's `setTimeout` sets `this` to the `Timeout` instance it returned,\n      // but web `setTimeout` always sets `this` to `window`\n      // see: https://developer.mozilla.org/en-US/docs/Web/API/Window/setTimeout#the_this_problem\n      return callback.apply(globalThis, args)\n    } finally {\n      // On certain older node versions (<20.16.0, <22.4.0),\n      // a `setTimeout` whose Timeout was converted to a primitive will leak.\n      // See: https://github.com/nodejs/node/issues/53335\n      // We can work around this by explicitly calling `clearTimeout` after the callback runs.\n      clearTimeout(timeout)\n    }\n  }\n  const timeout = setTimeout(wrappedCallback, ms)\n  return timeout[Symbol.toPrimitive]()\n}\n\nexport const intervalsManager = new IntervalsManager()\nexport const timeoutsManager = new TimeoutsManager()\n"], "names": ["intervalsManager", "timeouts<PERSON><PERSON><PERSON>", "ResourceManager", "add", "resourceArgs", "resource", "create", "resources", "push", "remove", "filter", "r", "destroy", "removeAll", "for<PERSON>ach", "IntervalsManager", "args", "webSetIntervalPolyfill", "interval", "clearInterval", "TimeoutsManager", "webSetTimeoutPolyfill", "timeout", "clearTimeout", "callback", "ms", "setInterval", "apply", "globalThis", "Symbol", "toPrimitive", "wrappedCallback", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;IAuFaA,gBAAgB;eAAhBA;;IACAC,eAAe;eAAfA;;;AAxFb,MAAeC;IAMbC,IAAIC,YAAkB,EAAE;QACtB,MAAMC,WAAW,IAAI,CAACC,MAAM,CAACF;QAC7B,IAAI,CAACG,SAAS,CAACC,IAAI,CAACH;QACpB,OAAOA;IACT;IAEAI,OAAOJ,QAAW,EAAE;QAClB,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACG,MAAM,CAAC,CAACC,IAAMA,MAAMN;QACpD,IAAI,CAACO,OAAO,CAACP;IACf;IAEAQ,YAAY;QACV,IAAI,CAACN,SAAS,CAACO,OAAO,CAAC,IAAI,CAACF,OAAO;QACnC,IAAI,CAACL,SAAS,GAAG,EAAE;IACrB;;aAnBQA,YAAiB,EAAE;;AAoB7B;AAEA,MAAMQ,yBAAyBb;IAI7BI,OAAOU,IAAoC,EAAE;QAC3C,4DAA4D;QAC5D,OAAOC,0BAA0BD;IACnC;IAEAJ,QAAQM,QAAgB,EAAE;QACxBC,cAAcD;IAChB;AACF;AAEA,MAAME,wBAAwBlB;IAI5BI,OAAOU,IAAmC,EAAE;QAC1C,2DAA2D;QAC3D,OAAOK,yBAAyBL;IAClC;IAEAJ,QAAQU,OAAe,EAAE;QACvBC,aAAaD;IACf;AACF;AAEA,SAASL,uBACPO,QAAkC,EAClCC,EAAW,EACX,GAAGT,IAAW;IAEd,OAAOU,YAAY;QACjB,0EAA0E;QAC1E,uDAAuD;QACvD,4FAA4F;QAC5F,OAAOF,SAASG,KAAK,CAACC,YAAYZ;IACpC,GAAGS,GAAG,CAACI,OAAOC,WAAW,CAAC;AAC5B;AAEA,SAAST,sBACPG,QAAkC,EAClCC,EAAW,EACX,GAAGT,IAAW;IAEd,MAAMe,kBAAkB;QACtB,IAAI;YACF,yEAAyE;YACzE,sDAAsD;YACtD,2FAA2F;YAC3F,OAAOP,SAASG,KAAK,CAACC,YAAYZ;QACpC,SAAU;YACR,sDAAsD;YACtD,uEAAuE;YACvE,mDAAmD;YACnD,wFAAwF;YACxFO,aAAaD;QACf;IACF;IACA,MAAMA,UAAUU,WAAWD,iBAAiBN;IAC5C,OAAOH,OAAO,CAACO,OAAOC,WAAW,CAAC;AACpC;AAEO,MAAM9B,mBAAmB,IAAIe;AAC7B,MAAMd,kBAAkB,IAAImB"}