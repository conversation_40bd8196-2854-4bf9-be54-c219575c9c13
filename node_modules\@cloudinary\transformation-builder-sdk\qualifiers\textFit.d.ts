import { Qualifier } from "../internal/qualifier/Qualifier.js";
/**
 * @description Contains functions that Applies automatic multi-line text wrap.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/layers#adding_multi_line_text|Adding multi line text}
 * @memberOf Qualifiers
 * @namespace TextFitQualifier
 */
declare class TextFitQualifier extends Qualifier {
    protected _height: number;
    protected _width: number;
    constructor(width: number, height?: number);
    height(height: number): this;
    toString(): string;
}
/**
 * @summary qualifier Adding an automatic multi-line text wrap.
 * @memberOf Qualifiers.TextFitQualifier
 * @param {number} width The width in pixels.
 * @param {number} height The height in pixels.
 */
declare function size(width: number, height?: number): TextFitQualifier;
declare const TextFit: {
    size: typeof size;
};
export { TextFit, size, TextFitQualifier };
