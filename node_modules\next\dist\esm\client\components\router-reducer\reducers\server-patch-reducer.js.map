{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "sourcesContent": ["import { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ServerPatchAction,\n  ReducerState,\n  ReadonlyReducerState,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyFlightData } from '../apply-flight-data'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\n\nexport function serverPatchReducer(\n  state: ReadonlyReducerState,\n  action: ServerPatchAction\n): ReducerState {\n  const {\n    serverResponse: { flightData, canonicalUrl: canonicalUrlOverride },\n    navigatedAt,\n  } = action\n\n  const mutable: Mutable = {}\n\n  mutable.preserveCustomHistoryState = false\n\n  // Handle case when navigating to page in `pages` from `app`\n  if (typeof flightData === 'string') {\n    return handleExternalUrl(\n      state,\n      mutable,\n      flightData,\n      state.pushRef.pendingPush\n    )\n  }\n\n  let currentTree = state.tree\n  let currentCache = state.cache\n\n  for (const normalizedFlightData of flightData) {\n    const { segmentPath: flightSegmentPath, tree: treePatch } =\n      normalizedFlightData\n\n    const newTree = applyRouterStatePatchToTree(\n      // TODO-APP: remove ''\n      ['', ...flightSegmentPath],\n      currentTree,\n      treePatch,\n      state.canonicalUrl\n    )\n\n    // `applyRouterStatePatchToTree` returns `null` when it determined that the server response is not applicable to the current tree.\n    // In other words, the server responded with a tree that doesn't match what the client is currently rendering.\n    // This can happen if the server patch action took longer to resolve than a subsequent navigation which would have changed the tree.\n    // Previously this case triggered an MPA navigation but it should be safe to simply discard the server response rather than forcing\n    // the entire page to reload.\n    if (newTree === null) {\n      return state\n    }\n\n    if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n      return handleExternalUrl(\n        state,\n        mutable,\n        state.canonicalUrl,\n        state.pushRef.pendingPush\n      )\n    }\n\n    const canonicalUrlOverrideHref = canonicalUrlOverride\n      ? createHrefFromUrl(canonicalUrlOverride)\n      : undefined\n\n    if (canonicalUrlOverrideHref) {\n      mutable.canonicalUrl = canonicalUrlOverrideHref\n    }\n\n    const cache: CacheNode = createEmptyCacheNode()\n    applyFlightData(navigatedAt, currentCache, cache, normalizedFlightData)\n\n    mutable.patchedTree = newTree\n    mutable.cache = cache\n\n    currentCache = cache\n    currentTree = newTree\n  }\n\n  return handleMutable(state, mutable)\n}\n"], "names": ["createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "applyFlightData", "handleMutable", "createEmptyCacheNode", "serverPatchReducer", "state", "action", "serverResponse", "flightData", "canonicalUrl", "canonicalUrlOverride", "navigatedAt", "mutable", "preserveCustomHistoryState", "pushRef", "pendingPush", "currentTree", "tree", "currentCache", "cache", "normalizedFlightData", "segmentPath", "flightSegmentPath", "treePatch", "newTree", "canonicalUrlOverrideHref", "undefined", "patchedTree"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,oBAAoB,QAAQ,mBAAkB;AAEvD,OAAO,SAASC,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EACJC,gBAAgB,EAAEC,UAAU,EAAEC,cAAcC,oBAAoB,EAAE,EAClEC,WAAW,EACZ,GAAGL;IAEJ,MAAMM,UAAmB,CAAC;IAE1BA,QAAQC,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOL,eAAe,UAAU;QAClC,OAAOR,kBACLK,OACAO,SACAJ,YACAH,MAAMS,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcX,MAAMY,IAAI;IAC5B,IAAIC,eAAeb,MAAMc,KAAK;IAE9B,KAAK,MAAMC,wBAAwBZ,WAAY;QAC7C,MAAM,EAAEa,aAAaC,iBAAiB,EAAEL,MAAMM,SAAS,EAAE,GACvDH;QAEF,MAAMI,UAAU1B,4BACd,sBAAsB;QACtB;YAAC;eAAOwB;SAAkB,EAC1BN,aACAO,WACAlB,MAAMI,YAAY;QAGpB,kIAAkI;QAClI,8GAA8G;QAC9G,oIAAoI;QACpI,mIAAmI;QACnI,6BAA6B;QAC7B,IAAIe,YAAY,MAAM;YACpB,OAAOnB;QACT;QAEA,IAAIN,4BAA4BiB,aAAaQ,UAAU;YACrD,OAAOxB,kBACLK,OACAO,SACAP,MAAMI,YAAY,EAClBJ,MAAMS,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMU,2BAA2Bf,uBAC7Bb,kBAAkBa,wBAClBgB;QAEJ,IAAID,0BAA0B;YAC5Bb,QAAQH,YAAY,GAAGgB;QACzB;QAEA,MAAMN,QAAmBhB;QACzBF,gBAAgBU,aAAaO,cAAcC,OAAOC;QAElDR,QAAQe,WAAW,GAAGH;QACtBZ,QAAQO,KAAK,GAAGA;QAEhBD,eAAeC;QACfH,cAAcQ;IAChB;IAEA,OAAOtB,cAAcG,OAAOO;AAC9B"}