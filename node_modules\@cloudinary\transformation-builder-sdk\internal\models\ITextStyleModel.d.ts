import { IQualifierModel } from "./IQualifierModel.js";
import { IStrokeModel } from "./IStrokeModel.js";
export interface ITextStyleModel extends IQualifierModel {
    fontFamily: string;
    fontSize: number | string;
    fontWeight?: string;
    fontStyle?: string;
    fontAntialias?: string;
    fontHinting?: string;
    textDecoration?: string;
    textAlignment?: string;
    stroke?: IStrokeModel;
    letterSpacing?: number;
    lineSpacing?: number;
}
