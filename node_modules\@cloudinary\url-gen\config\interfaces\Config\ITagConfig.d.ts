/**
 * @description - Not currently in use
 * @private
 */
interface ITagConfig {
    hiDpi?: boolean;
    clientHints?: boolean;
    unsignedUpload?: boolean;
    voidClosingSlash?: boolean;
    sortAttributes?: boolean;
    videoPosterFormat?: string;
    quotesType?: string;
    contentDelimiter: string;
    responsive: {
        isResponsive: boolean;
        responsiveClass?: string;
        responsiveWidth?: boolean;
        responsivePlaceholder?: string;
    };
}
export default ITagConfig;
