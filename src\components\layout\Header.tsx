'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { APP_NAME, ROUTES } from '@/constants'

export function Header() {
  return (
    <header className="border-b border-gray-200 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href={ROUTES.HOME} className="text-xl font-bold text-gray-900">
              {APP_NAME}
            </Link>
          </div>
          
          <nav className="hidden md:flex space-x-8">
            <Link href={ROUTES.HOME} className="text-gray-700 hover:text-gray-900">
              Home
            </Link>
            <Link href={ROUTES.PRODUCTS} className="text-gray-700 hover:text-gray-900">
              Products
            </Link>
          </nav>
          
          <div className="flex items-center space-x-4">
            <Link href={ROUTES.LOGIN}>
              <Button variant="ghost">Login</Button>
            </Link>
            <Link href={ROUTES.REGISTER}>
              <Button>Sign Up</Button>
            </Link>
          </div>
        </div>
      </div>
    </header>
  )
}
