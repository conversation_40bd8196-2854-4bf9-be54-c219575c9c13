import { BaseTextSource } from "./BaseTextSource.js";
import { TextStyle } from "../../textStyle.js";
import { ITransformationFromJson } from "../../../internal/models/IHasFromJson.js";
import { ITextSourceModel } from "../../../internal/models/ITextSourceModel.js";
/**
 * @memberOf Qualifiers.Source
 * @extends {Qualifiers.Source.BaseTextSource}
 * @description Defines how to manipulate a text layer
 */
declare class TextSource extends BaseTextSource {
    constructor(fileName: string, textStyle: TextStyle | string);
    static fromJson(qualifierModel: ITextSourceModel, transformationFromJson: ITransformationFromJson): TextSource;
}
export { TextSource };
