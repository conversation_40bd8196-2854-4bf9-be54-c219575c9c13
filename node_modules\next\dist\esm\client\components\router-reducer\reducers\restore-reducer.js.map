{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "sourcesContent": ["import { createHrefFromUrl } from '../create-href-from-url'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  RestoreAction,\n} from '../router-reducer-types'\nimport { extractPathFromFlightRouterState } from '../compute-changed-path'\nimport { updateCacheNodeOnPopstateRestoration } from '../ppr-navigations'\n\nexport function restoreReducer(\n  state: ReadonlyReducerState,\n  action: RestoreAction\n): ReducerState {\n  const { url, tree } = action\n  const href = createHrefFromUrl(url)\n  // This action is used to restore the router state from the history state.\n  // However, it's possible that the history state no longer contains the `FlightRouterState`.\n  // We will copy over the internal state on pushState/replaceState events, but if a history entry\n  // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n  // the history state will not contain the `FlightRouterState`.\n  // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n  const treeToRestore = tree || state.tree\n\n  const oldCache = state.cache\n  const newCache = process.env.__NEXT_PPR\n    ? // When PPR is enabled, we update the cache to drop the prefetch\n      // data for any segment whose dynamic data was already received. This\n      // prevents an unnecessary flash back to PPR state during a\n      // back/forward navigation.\n      updateCacheNodeOnPopstateRestoration(oldCache, treeToRestore)\n    : oldCache\n\n  return {\n    // Set canonical url\n    canonicalUrl: href,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // Ensures that the custom history state that was set is preserved when applying this update.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: state.focusAndScrollRef,\n    cache: newCache,\n    prefetchCache: state.prefetchCache,\n    // Restore provided tree\n    tree: treeToRestore,\n    nextUrl: extractPathFromFlightRouterState(treeToRestore) ?? url.pathname,\n  }\n}\n"], "names": ["createHrefFromUrl", "extractPathFromFlightRouterState", "updateCacheNodeOnPopstateRestoration", "restoreReducer", "state", "action", "url", "tree", "href", "treeToRestore", "<PERSON><PERSON><PERSON>", "cache", "newCache", "process", "env", "__NEXT_PPR", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "prefetchCache", "nextUrl", "pathname"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAM3D,SAASC,gCAAgC,QAAQ,0BAAyB;AAC1E,SAASC,oCAAoC,QAAQ,qBAAoB;AAEzE,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOR,kBAAkBM;IAC/B,0EAA0E;IAC1E,4FAA4F;IAC5F,gGAAgG;IAChG,6FAA6F;IAC7F,8DAA8D;IAC9D,yGAAyG;IACzG,MAAMG,gBAAgBF,QAAQH,MAAMG,IAAI;IAExC,MAAMG,WAAWN,MAAMO,KAAK;IAC5B,MAAMC,WAAWC,QAAQC,GAAG,CAACC,UAAU,GAEnC,qEAAqE;IACrE,2DAA2D;IAC3D,2BAA2B;IAC3Bb,qCAAqCQ,UAAUD,iBAC/CC;QAgBOT;IAdX,OAAO;QACL,oBAAoB;QACpBe,cAAcR;QACdS,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBjB,MAAMiB,iBAAiB;QAC1CV,OAAOC;QACPU,eAAelB,MAAMkB,aAAa;QAClC,wBAAwB;QACxBf,MAAME;QACNc,SAAStB,CAAAA,oCAAAA,iCAAiCQ,0BAAjCR,oCAAmDK,IAAIkB,QAAQ;IAC1E;AACF"}