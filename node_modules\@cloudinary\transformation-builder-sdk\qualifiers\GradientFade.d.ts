/**
 * @description Defines how the background gradient fade effect is applied.
 * @memberOf Qualifiers
 * @namespace GradientFade
 */
/**
 * @summary qualifier
 * @description Instructs the gradient fade to be applied symmetrically (to opposite edges of the image).
 * @memberOf Qualifiers.GradientFade
 * @return {string}
 */
declare function symmetric(): string;
/**
 * @summary qualifier
 * @description Instructs the gradient fade to be applied symmetrically (to opposite edges of the image) including background padding.
 * @memberOf Qualifiers.GradientFade
 * @return {string}
 */
declare function symmetricPad(): string;
declare const GradientFade: {
    symmetric: typeof symmetric;
    symmetricPad: typeof symmetricPad;
};
export { GradientFade, symmetricPad, symmetric };
