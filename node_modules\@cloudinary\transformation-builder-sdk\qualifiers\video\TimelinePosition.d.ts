import { Action } from "../../internal/Action.js";
import { StringNumberOrExpression } from "../../simpleTypes.js";
import { ITimelinePositionModel } from "../../internal/models/ITimelinePositionModel.js";
/**
 * @description Defines a video range using startOffset, endOffset, duration.
 * @namespace TimelinePosition
 * @memberOf Qualifiers
 */
/**
 * TimelinePosition
 * @memberOf Qualifiers.TimelinePosition
 */
declare class TimelinePosition extends Action {
    protected _actionModel: ITimelinePositionModel;
    constructor();
    /**
     * @param {string | number} startOffset
     */
    startOffset(startOffset: StringNumberOrExpression): this;
    /**
     * @param {string | number} endOffset
     */
    endOffset(endOffset: StringNumberOrExpression): this;
    /**
     * @param {string | number} duration
     */
    duration(duration: StringNumberOrExpression): this;
}
export { TimelinePosition };
