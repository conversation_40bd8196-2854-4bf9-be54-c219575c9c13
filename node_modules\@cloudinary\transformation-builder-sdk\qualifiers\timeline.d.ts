import { TimelinePosition } from "./video/TimelinePosition.js";
/**
 * @memberOf Qualifiers
 * @description When applying an overlay on a video, this qualifier controls when this overlay is attached
 * @namespace TimelinePosition
 * @see {@link Actions.Overlay| The overlay action}
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.TimelinePosition
 * @return {Qualifiers.TimelinePosition.TimelinePosition}
 */
declare function position(): TimelinePosition;
declare const Timeline: {
    position: typeof position;
};
export { Timeline, position };
