1:"$Sreact.fragment"
2:I[4518,["254","static/chunks/254-de61af55b27110a1.js","177","static/chunks/app/layout-b6d008d6ddc860b8.js"],"ReduxProvider"]
3:I[5430,["254","static/chunks/254-de61af55b27110a1.js","177","static/chunks/app/layout-b6d008d6ddc860b8.js"],"Header"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[894,[],"ClientPageRoot"]
7:I[3792,["254","static/chunks/254-de61af55b27110a1.js","974","static/chunks/app/page-4ab8d53e737e22c8.js"],"default"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[9665,[],"MetadataBoundary"]
13:I[6614,[],""]
:HL["/_next/static/css/a9563cb88ed991c7.css","style"]
0:{"P":null,"b":"DY_iFKG3UwMYnyoVObyM9","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/a9563cb88ed991c7.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","$L3",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"border-t border-gray-200 bg-white","children":["$","div",null,{"className":"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8","children":["$","div",null,{"className":"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0","children":[["$","div",null,{"className":"text-sm text-gray-600","children":["© ",2025," ","Caparan Tindahan",". All rights reserved."]}],["$","div",null,{"className":"flex space-x-6","children":[["$","a",null,{"href":"#","className":"text-sm text-gray-600 hover:text-gray-900","children":"Privacy Policy"}],["$","a",null,{"href":"#","className":"text-sm text-gray-600 hover:text-gray-900","children":"Terms of Service"}],["$","a",null,{"href":"#","className":"text-sm text-gray-600 hover:text-gray-900","children":"Contact"}]]}]]}]}]}]]}]}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L6",null,{"Component":"$7","searchParams":{},"params":{},"promises":["$@8","$@9"]}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","ZiJ3qO5x66fcAcBuW3rGWv",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],["$","$L11",null,{"children":"$L12"}]]}],false]],"m":"$undefined","G":["$13","$undefined"],"s":false,"S":true}
14:"$Sreact.suspense"
15:I[4911,[],"AsyncMetadata"]
8:{}
9:{}
12:["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":["$","$L15",null,{"promise":"$@16"}]}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
e:{"metadata":[["$","title","0",{"children":"Caparan Tindahan"}],["$","meta","1",{"name":"description","content":"Professional e-commerce platform built with Next.js 15"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
16:{"metadata":"$e:metadata","error":null,"digest":"$undefined"}
