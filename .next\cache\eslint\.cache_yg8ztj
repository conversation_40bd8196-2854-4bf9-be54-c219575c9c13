[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Footer.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Header.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Layout.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\providers\\ReduxProvider.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\ui\\Button.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\ui\\Card.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\ui\\Input.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\constants\\index.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\hooks\\useSupabase.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\lib\\features\\auth\\authSlice.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\lib\\store.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\lib\\supabase.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\types\\index.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\utils\\cn.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\utils\\index.ts": "17"}, {"size": 907, "mtime": 1750756334789, "results": "18", "hashOfConfig": "19"}, {"size": 4648, "mtime": 1750756383197, "results": "20", "hashOfConfig": "19"}, {"size": 929, "mtime": 1750755937751, "results": "21", "hashOfConfig": "19"}, {"size": 1234, "mtime": 1750755928538, "results": "22", "hashOfConfig": "19"}, {"size": 352, "mtime": 1750755946328, "results": "23", "hashOfConfig": "19"}, {"size": 275, "mtime": 1750755728570, "results": "24", "hashOfConfig": "19"}, {"size": 1996, "mtime": 1750755862225, "results": "25", "hashOfConfig": "19"}, {"size": 1865, "mtime": 1750756407845, "results": "26", "hashOfConfig": "19"}, {"size": 1232, "mtime": 1750756396886, "results": "27", "hashOfConfig": "19"}, {"size": 799, "mtime": 1750755831159, "results": "28", "hashOfConfig": "19"}, {"size": 360, "mtime": 1750755677532, "results": "29", "hashOfConfig": "19"}, {"size": 899, "mtime": 1750755737896, "results": "30", "hashOfConfig": "19"}, {"size": 757, "mtime": 1750755749180, "results": "31", "hashOfConfig": "19"}, {"size": 465, "mtime": 1750755671232, "results": "32", "hashOfConfig": "19"}, {"size": 463, "mtime": 1750755810740, "results": "33", "hashOfConfig": "19"}, {"size": 116, "mtime": 1750755870102, "results": "34", "hashOfConfig": "19"}, {"size": 939, "mtime": 1750755819387, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b1oqc5", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\providers\\ReduxProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\hooks\\useSupabase.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\lib\\features\\auth\\authSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\lib\\store.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\utils\\index.ts", ["87", "88"], [], {"ruleId": "89", "severity": 1, "message": "90", "line": 27, "column": 46, "nodeType": "91", "messageId": "92", "endLine": 27, "endColumn": 49, "suggestions": "93"}, {"ruleId": "89", "severity": 1, "message": "90", "line": 27, "column": 56, "nodeType": "91", "messageId": "92", "endLine": 27, "endColumn": 59, "suggestions": "94"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["95", "96"], ["97", "98"], {"messageId": "99", "fix": "100", "desc": "101"}, {"messageId": "102", "fix": "103", "desc": "104"}, {"messageId": "99", "fix": "105", "desc": "101"}, {"messageId": "102", "fix": "106", "desc": "104"}, "suggestUnknown", {"range": "107", "text": "108"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "109", "text": "110"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "111", "text": "108"}, {"range": "112", "text": "110"}, [701, 704], "unknown", [701, 704], "never", [711, 714], [711, 714]]