<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/a9563cb88ed991c7.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-8ba223b515e4ba9a.js"/><script src="/_next/static/chunks/4bd1b696-385700c3983b9140.js" async=""></script><script src="/_next/static/chunks/684-0e9ba1c4a51de074.js" async=""></script><script src="/_next/static/chunks/main-app-aabe50a75d1d1cb5.js" async=""></script><script src="/_next/static/chunks/254-de61af55b27110a1.js" async=""></script><script src="/_next/static/chunks/app/layout-b6d008d6ddc860b8.js" async=""></script><script src="/_next/static/chunks/app/page-4ab8d53e737e22c8.js" async=""></script><title>Caparan Tindahan</title><meta name="description" content="Professional e-commerce platform built with Next.js 15"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen flex flex-col"><header class="border-b border-gray-200 bg-white"><div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8"><div class="flex h-16 items-center justify-between"><div class="flex items-center"><a class="text-xl font-bold text-gray-900" href="/">Caparan Tindahan</a></div><nav class="hidden md:flex space-x-8"><a class="text-gray-700 hover:text-gray-900" href="/">Home</a><a class="text-gray-700 hover:text-gray-900" href="/products">Products</a></nav><div class="flex items-center space-x-4"><a href="/login"><button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none hover:bg-gray-100 h-10 px-4">Login</button></a><a href="/register"><button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none bg-blue-600 text-white hover:bg-blue-700 h-10 px-4">Sign Up</button></a></div></div></div></header><main class="flex-1"><div class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen"><section class="py-20 px-4 sm:px-6 lg:px-8"><div class="max-w-7xl mx-auto text-center"><h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Welcome to <span class="text-blue-600">Caparan Tindahan</span></h1><p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">A professional e-commerce platform built with Next.js 15, TypeScript, Tailwind CSS, Redux Toolkit, and Supabase. Experience modern web development at its finest.</p><div class="flex flex-col sm:flex-row gap-4 justify-center"><button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none bg-blue-600 text-white hover:bg-blue-700 h-12 px-6 text-lg">Get Started</button><button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-300 bg-transparent hover:bg-gray-50 h-12 px-6 text-lg">Learn More</button></div></div></section><section class="py-16 px-4 sm:px-6 lg:px-8 bg-white"><div class="max-w-7xl mx-auto"><h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Tech Stack Features</h2><div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8"><div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><div class="text-3xl mb-4">⚡</div><h3 class="text-xl font-semibold text-gray-900 mb-2">Next.js 15</h3><p class="text-gray-600">Latest version with App Router, Server Components, and Turbopack for blazing fast development.</p></div><div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><div class="text-3xl mb-4">🔒</div><h3 class="text-xl font-semibold text-gray-900 mb-2">TypeScript</h3><p class="text-gray-600">Full type safety across the entire application for better developer experience and fewer bugs.</p></div><div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><div class="text-3xl mb-4">🎨</div><h3 class="text-xl font-semibold text-gray-900 mb-2">Tailwind CSS</h3><p class="text-gray-600">Utility-first CSS framework for rapid UI development with consistent design.</p></div><div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><div class="text-3xl mb-4">🔄</div><h3 class="text-xl font-semibold text-gray-900 mb-2">Redux Toolkit</h3><p class="text-gray-600">Predictable state management with modern Redux patterns and excellent DevTools.</p></div><div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><div class="text-3xl mb-4">🗄️</div><h3 class="text-xl font-semibold text-gray-900 mb-2">Supabase</h3><p class="text-gray-600">Open source Firebase alternative with PostgreSQL database and real-time features.</p></div><div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><div class="text-3xl mb-4">⚙️</div><h3 class="text-xl font-semibold text-gray-900 mb-2">Professional Setup</h3><p class="text-gray-600">ESLint, Prettier, and VS Code configuration for optimal development workflow.</p></div></div></div></section><section class="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50"><div class="max-w-4xl mx-auto text-center"><h2 class="text-3xl font-bold text-gray-900 mb-8">Installation Status</h2><div class="bg-white rounded-lg shadow-lg p-8"><div class="grid md:grid-cols-2 gap-6"><div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"><span class="font-medium text-gray-900">Next.js 15 + TypeScript</span><span class="text-lg">✅</span></div><div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"><span class="font-medium text-gray-900">Tailwind CSS v4</span><span class="text-lg">✅</span></div><div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"><span class="font-medium text-gray-900">Redux Toolkit</span><span class="text-lg">✅</span></div><div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"><span class="font-medium text-gray-900">Supabase Integration</span><span class="text-lg">✅</span></div><div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"><span class="font-medium text-gray-900">ESLint + Prettier</span><span class="text-lg">✅</span></div><div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"><span class="font-medium text-gray-900">Professional Structure</span><span class="text-lg">✅</span></div></div><p class="mt-6 text-gray-600">Authentication Status: <!-- -->🔴 Not Logged In</p></div></div></section></div><!--$--><!--/$--></main><footer class="border-t border-gray-200 bg-white"><div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8"><div class="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0"><div class="text-sm text-gray-600">© <!-- -->2025<!-- --> <!-- -->Caparan Tindahan<!-- -->. All rights reserved.</div><div class="flex space-x-6"><a href="#" class="text-sm text-gray-600 hover:text-gray-900">Privacy Policy</a><a href="#" class="text-sm text-gray-600 hover:text-gray-900">Terms of Service</a><a href="#" class="text-sm text-gray-600 hover:text-gray-900">Contact</a></div></div></div></footer></div><script src="/_next/static/chunks/webpack-8ba223b515e4ba9a.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[4518,[\"254\",\"static/chunks/254-de61af55b27110a1.js\",\"177\",\"static/chunks/app/layout-b6d008d6ddc860b8.js\"],\"ReduxProvider\"]\n3:I[5430,[\"254\",\"static/chunks/254-de61af55b27110a1.js\",\"177\",\"static/chunks/app/layout-b6d008d6ddc860b8.js\"],\"Header\"]\n4:I[7555,[],\"\"]\n5:I[1295,[],\"\"]\n6:I[894,[],\"ClientPageRoot\"]\n7:I[3792,[\"254\",\"static/chunks/254-de61af55b27110a1.js\",\"974\",\"static/chunks/app/page-4ab8d53e737e22c8.js\"],\"default\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[9665,[],\"MetadataBoundary\"]\n13:I[6614,[],\"\"]\n:HL[\"/_next/static/css/a9563cb88ed991c7.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"DY_iFKG3UwMYnyoVObyM9\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/a9563cb88ed991c7.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen flex flex-col\",\"children\":[[\"$\",\"$L3\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"footer\",null,{\"className\":\"border-t border-gray-200 bg-white\",\"children\":[\"$\",\"div\",null,{\"className\":\"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":[\"© \",2025,\" \",\"Caparan Tindahan\",\". All rights reserved.\"]}],[\"$\",\"div\",null,{\"className\":\"flex space-x-6\",\"children\":[[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-sm text-gray-600 hover:text-gray-900\",\"children\":\"Privacy Policy\"}],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-sm text-gray-600 hover:text-gray-900\",\"children\":\"Terms of Service\"}],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-sm text-gray-600 hover:text-gray-900\",\"children\":\"Contact\"}]]}]]}]}]}]]}]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L6\",null,{\"Component\":\"$7\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@8\",\"$@9\"]}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"ZiJ3qO5x66fcAcBuW3rGWv\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],[\"$\",\"$L11\",null,{\"children\":\"$L12\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$13\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"14:\"$Sreact.suspense\"\n15:I[4911,[],\"AsyncMetadata\"]\n8:{}\n9:{}\n12:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$14\",null,{\"fallback\":null,\"children\":[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Caparan Tindahan\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Professional e-commerce platform built with Next.js 15\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n16:{\"metadata\":\"$e:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>