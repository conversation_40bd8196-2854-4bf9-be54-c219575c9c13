/**
 * @description Qualifiers for applying an ordered dither filter to the image.
 * @namespace StreamingProfile
 * @memberOf Qualifiers
 * @see Visit {@link Actions
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.StreamingProfile
 * @return {string}
 */
declare function fullHd(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.StreamingProfile
 * @return {string}
 */
declare function hd(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.StreamingProfile
 * @return {string}
 */
declare function sd(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.StreamingProfile
 * @return {string}
 */
declare function fullHdWifi(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.StreamingProfile
 * @return {string}
 */
declare function fullHdLean(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.StreamingProfile
 * @return {string}
 */
declare function hdLean(): string;
declare const StreamingProfile: {
    hd: typeof hd;
    sd: typeof sd;
    hdLean: typeof hdLean;
    fullHd: typeof fullHd;
    fullHdLean: typeof fullHdLean;
    fullHdWifi: typeof fullHdWifi;
};
export { hd, sd, hdLean, fullHd, fullHdLean, fullHdWifi, StreamingProfile };
