import { supabase, dbFunctions } from '@/lib/supabase'
import type { DebtTransaction, DebtFormData, DebtFilters, PaginationParams, PaginatedResponse, Customer, Product } from '@/types'

export const debtService = {
  // Get all debt transactions with pagination and filters
  getDebts: async (
    params: PaginationParams & DebtFilters = { page: 1, limit: 10 }
  ): Promise<PaginatedResponse<DebtTransaction>> => {
    const { page, limit, search, customer_id, is_fully_paid, date_from, date_to, sort_by = 'debt_date', sort_order = 'desc' } = params
    
    let query = supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `, { count: 'exact' })

    // Apply filters
    if (search) {
      query = query.or(`product_name.ilike.%${search}%,customers.full_name.ilike.%${search}%`)
    }
    
    if (customer_id) {
      query = query.eq('customer_id', customer_id)
    }
    
    if (typeof is_fully_paid === 'boolean') {
      query = query.eq('is_fully_paid', is_fully_paid)
    }
    
    if (date_from) {
      query = query.gte('debt_date', date_from)
    }
    
    if (date_to) {
      query = query.lte('debt_date', date_to)
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(error.message)
    }

    return {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    }
  },

  // Get single debt transaction by ID
  getDebt: async (id: string): Promise<DebtTransaction | null> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }

    return data
  },

  // Create new debt transaction using database function
  createDebt: async (debtData: DebtFormData): Promise<DebtTransaction> => {
    const { data, error } = await dbFunctions.createDebtTransaction({
      customer_id: debtData.customer_id,
      product_id: debtData.product_id,
      quantity: debtData.quantity,
      notes: debtData.notes
    })

    if (error) {
      throw new Error(error.message)
    }

    if (!data?.success) {
      throw new Error(data?.error || 'Failed to create debt transaction')
    }

    // Fetch the created debt transaction with related data
    const createdDebt = await debtService.getDebt(data.debt_id)
    if (!createdDebt) {
      throw new Error('Failed to retrieve created debt transaction')
    }

    return createdDebt
  },

  // Update debt transaction
  updateDebt: async (id: string, debtData: Partial<DebtFormData>): Promise<DebtTransaction> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .update({
        notes: debtData.notes,
        // Only allow updating notes for safety
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  // Delete debt transaction (only if not paid)
  deleteDebt: async (id: string): Promise<void> => {
    // Check if debt is already paid
    const debt = await debtService.getDebt(id)
    if (!debt) {
      throw new Error('Debt transaction not found')
    }

    if (debt.remaining_balance !== debt.total_amount) {
      throw new Error('Cannot delete debt transaction with payments')
    }

    const { error } = await supabase
      .from('debt_transactions')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(error.message)
    }
  },

  // Get outstanding debts for a customer
  getCustomerOutstandingDebts: async (customerId: string): Promise<DebtTransaction[]> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .eq('customer_id', customerId)
      .eq('is_fully_paid', false)
      .order('debt_date', { ascending: true })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get debt statistics
  getDebtStats: async () => {
    const [
      { count: totalDebts },
      { count: unpaidDebts },
      { data: totalDebtAmount },
      { data: unpaidDebtAmount }
    ] = await Promise.all([
      supabase.from('debt_transactions').select('*', { count: 'exact', head: true }),
      supabase.from('debt_transactions').select('*', { count: 'exact', head: true }).eq('is_fully_paid', false),
      supabase.from('debt_transactions').select('total_amount'),
      supabase.from('debt_transactions').select('remaining_balance').eq('is_fully_paid', false)
    ])

    const totalAmount = totalDebtAmount?.reduce((sum, debt) => sum + (debt.total_amount || 0), 0) || 0
    const unpaidAmount = unpaidDebtAmount?.reduce((sum, debt) => sum + (debt.remaining_balance || 0), 0) || 0
    const paidAmount = totalAmount - unpaidAmount

    return {
      total_debts: totalDebts || 0,
      unpaid_debts: unpaidDebts || 0,
      paid_debts: (totalDebts || 0) - (unpaidDebts || 0),
      total_debt_amount: totalAmount,
      unpaid_debt_amount: unpaidAmount,
      paid_debt_amount: paidAmount,
      payment_rate: totalDebts ? ((totalDebts - unpaidDebts) / totalDebts) * 100 : 0
    }
  },

  // Get recent debt transactions
  getRecentDebts: async (limit: number = 10): Promise<DebtTransaction[]> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .order('debt_date', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get overdue debts (older than 30 days)
  getOverdueDebts: async (days: number = 30): Promise<DebtTransaction[]> => {
    const overdueDate = new Date()
    overdueDate.setDate(overdueDate.getDate() - days)

    const { data, error } = await supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .eq('is_fully_paid', false)
      .lt('debt_date', overdueDate.toISOString())
      .order('debt_date', { ascending: true })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get debt transactions by date range
  getDebtsByDateRange: async (startDate: string, endDate: string): Promise<DebtTransaction[]> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .gte('debt_date', startDate)
      .lte('debt_date', endDate)
      .order('debt_date', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get top customers by debt amount
  getTopDebtors: async (limit: number = 10): Promise<Customer[]> => {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .gt('total_debt', 0)
      .eq('is_active', true)
      .order('total_debt', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get debt summary by product
  getDebtSummaryByProduct: async (): Promise<any[]> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .select(`
        product_name,
        product_id,
        total_amount.sum(),
        remaining_balance.sum(),
        quantity.sum()
      `)
      .group('product_name, product_id')
      .order('total_amount.sum()', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Export debt data
  exportDebts: async (filters: DebtFilters = {}): Promise<DebtTransaction[]> => {
    let query = supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)

    // Apply filters
    if (filters.customer_id) {
      query = query.eq('customer_id', filters.customer_id)
    }
    
    if (typeof filters.is_fully_paid === 'boolean') {
      query = query.eq('is_fully_paid', filters.is_fully_paid)
    }
    
    if (filters.date_from) {
      query = query.gte('debt_date', filters.date_from)
    }
    
    if (filters.date_to) {
      query = query.lte('debt_date', filters.date_to)
    }

    query = query.order('debt_date', { ascending: false })

    const { data, error } = await query

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Mark debt as fully paid (for manual adjustments)
  markDebtAsPaid: async (id: string): Promise<DebtTransaction> => {
    const { data, error } = await supabase
      .from('debt_transactions')
      .update({
        remaining_balance: 0,
        is_fully_paid: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  }
}
