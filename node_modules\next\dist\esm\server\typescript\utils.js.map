{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "sourcesContent": ["import type { VirtualTypeScriptEnvironment } from 'next/dist/compiled/@typescript/vfs'\nimport {\n  createFSBackedSystem,\n  createDefaultMapFromNodeModules,\n  createVirtualTypeScriptEnvironment,\n} from 'next/dist/compiled/@typescript/vfs'\n\nimport path, { join } from 'path'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\ntype TypeScript = typeof import('typescript/lib/tsserverlibrary')\n\nlet ts: TypeScript\nlet info: tsModule.server.PluginCreateInfo\nlet appDirRegExp: RegExp\nexport let virtualTsEnv: VirtualTypeScriptEnvironment\n\nexport function log(message: string) {\n  info.project.projectService.logger.info('[next] ' + message)\n}\n\n// This function has to be called initially.\nexport function init(opts: {\n  ts: TypeScript\n  info: tsModule.server.PluginCreateInfo\n}): boolean {\n  const projectDir = opts.info.project.getCurrentDirectory()\n  ts = opts.ts\n  info = opts.info\n  appDirRegExp = new RegExp(\n    '^' + (projectDir + '(/src)?/app').replace(/[\\\\/]/g, '[\\\\/]')\n  )\n\n  log('Initializing Next.js TypeScript plugin: ' + projectDir)\n\n  const compilerOptions = info.project.getCompilerOptions()\n  const fsMap = createDefaultMapFromNodeModules(\n    compilerOptions,\n    ts,\n    join(projectDir, 'node_modules/typescript/lib')\n  )\n  const system = createFSBackedSystem(fsMap, projectDir, ts)\n\n  virtualTsEnv = createVirtualTypeScriptEnvironment(\n    system,\n    [],\n    ts,\n    compilerOptions\n  )\n\n  if (!virtualTsEnv) {\n    log(\n      'Failed to create virtual TypeScript environment. This is a bug in Next.js TypeScript plugin. Please report it by opening an issue at https://github.com/vercel/next.js/issues.'\n    )\n    return false\n  }\n\n  log('Successfully initialized Next.js TypeScript plugin!')\n  return true\n}\n\nexport function getTs() {\n  return ts\n}\n\nexport function getInfo() {\n  return info\n}\n\nexport function getTypeChecker() {\n  return info.languageService.getProgram()?.getTypeChecker()\n}\n\nexport function getSource(fileName: string) {\n  return info.languageService.getProgram()?.getSourceFile(fileName)\n}\n\nexport function getSourceFromVirtualTsEnv(fileName: string) {\n  if (virtualTsEnv.sys.fileExists(fileName)) {\n    return virtualTsEnv.getSourceFile(fileName)\n  }\n  return getSource(fileName)\n}\n\nexport function removeStringQuotes(str: string): string {\n  return str.replace(/^['\"`]|['\"`]$/g, '')\n}\n\nexport const isPositionInsideNode = (position: number, node: tsModule.Node) => {\n  const start = node.getFullStart()\n  return start <= position && position <= node.getFullWidth() + start\n}\n\nexport const isDefaultFunctionExport = (\n  node: tsModule.Node\n): node is tsModule.FunctionDeclaration => {\n  if (ts.isFunctionDeclaration(node)) {\n    let hasExportKeyword = false\n    let hasDefaultKeyword = false\n\n    if (node.modifiers) {\n      for (const modifier of node.modifiers) {\n        if (modifier.kind === ts.SyntaxKind.ExportKeyword) {\n          hasExportKeyword = true\n        } else if (modifier.kind === ts.SyntaxKind.DefaultKeyword) {\n          hasDefaultKeyword = true\n        }\n      }\n    }\n\n    // `export default function`\n    if (hasExportKeyword && hasDefaultKeyword) {\n      return true\n    }\n  }\n  return false\n}\n\nexport const isInsideApp = (filePath: string) => {\n  return appDirRegExp.test(filePath)\n}\nexport const isAppEntryFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^(page|layout)\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\nexport const isPageFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^page\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\n\n// Check if a module is a client entry.\nexport function getEntryInfo(\n  fileName: string,\n  throwOnInvalidDirective?: boolean\n) {\n  const source = getSource(fileName)\n  if (source) {\n    let isDirective = true\n    let isClientEntry = false\n    let isServerEntry = false\n\n    ts.forEachChild(source!, (node) => {\n      if (\n        ts.isExpressionStatement(node) &&\n        ts.isStringLiteral(node.expression)\n      ) {\n        if (node.expression.text === 'use client') {\n          if (isDirective) {\n            isClientEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use client\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        } else if (node.expression.text === 'use server') {\n          if (isDirective) {\n            isServerEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use server\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        }\n\n        if (isClientEntry && isServerEntry) {\n          const e = {\n            messageText:\n              'Cannot use both \"use client\" and \"use server\" directives in the same file.',\n            start: node.expression.getStart(),\n            length: node.expression.getWidth(),\n          }\n          throw e\n        }\n      } else {\n        isDirective = false\n      }\n    })\n\n    return { client: isClientEntry, server: isServerEntry }\n  }\n\n  return { client: false, server: false }\n}\n"], "names": ["createFSBackedSystem", "createDefaultMapFromNodeModules", "createVirtualTypeScriptEnvironment", "path", "join", "ts", "info", "appDirRegExp", "virtualTsEnv", "log", "message", "project", "projectService", "logger", "init", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "compilerOptions", "getCompilerOptions", "fsMap", "system", "getTs", "getInfo", "getType<PERSON><PERSON>cker", "languageService", "getProgram", "getSource", "fileName", "getSourceFile", "getSourceFromVirtualTsEnv", "sys", "fileExists", "removeStringQuotes", "str", "isPositionInsideNode", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefaultFunctionExport", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "isInsideApp", "filePath", "test", "isAppEntryFile", "basename", "isPageFile", "getEntryInfo", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": "AACA,SACEA,oBAAoB,EACpBC,+BAA+B,EAC/BC,kCAAkC,QAC7B,qCAAoC;AAE3C,OAAOC,QAAQC,IAAI,QAAQ,OAAM;AAKjC,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,OAAO,IAAIC,aAA0C;AAErD,OAAO,SAASC,IAAIC,OAAe;IACjCJ,KAAKK,OAAO,CAACC,cAAc,CAACC,MAAM,CAACP,IAAI,CAAC,YAAYI;AACtD;AAEA,4CAA4C;AAC5C,OAAO,SAASI,KAAKC,IAGpB;IACC,MAAMC,aAAaD,KAAKT,IAAI,CAACK,OAAO,CAACM,mBAAmB;IACxDZ,KAAKU,KAAKV,EAAE;IACZC,OAAOS,KAAKT,IAAI;IAChBC,eAAe,IAAIW,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAGvDV,IAAI,6CAA6CO;IAEjD,MAAMI,kBAAkBd,KAAKK,OAAO,CAACU,kBAAkB;IACvD,MAAMC,QAAQrB,gCACZmB,iBACAf,IACAD,KAAKY,YAAY;IAEnB,MAAMO,SAASvB,qBAAqBsB,OAAON,YAAYX;IAEvDG,eAAeN,mCACbqB,QACA,EAAE,EACFlB,IACAe;IAGF,IAAI,CAACZ,cAAc;QACjBC,IACE;QAEF,OAAO;IACT;IAEAA,IAAI;IACJ,OAAO;AACT;AAEA,OAAO,SAASe;IACd,OAAOnB;AACT;AAEA,OAAO,SAASoB;IACd,OAAOnB;AACT;AAEA,OAAO,SAASoB;QACPpB;IAAP,QAAOA,mCAAAA,KAAKqB,eAAe,CAACC,UAAU,uBAA/BtB,iCAAmCoB,cAAc;AAC1D;AAEA,OAAO,SAASG,UAAUC,QAAgB;QACjCxB;IAAP,QAAOA,mCAAAA,KAAKqB,eAAe,CAACC,UAAU,uBAA/BtB,iCAAmCyB,aAAa,CAACD;AAC1D;AAEA,OAAO,SAASE,0BAA0BF,QAAgB;IACxD,IAAItB,aAAayB,GAAG,CAACC,UAAU,CAACJ,WAAW;QACzC,OAAOtB,aAAauB,aAAa,CAACD;IACpC;IACA,OAAOD,UAAUC;AACnB;AAEA,OAAO,SAASK,mBAAmBC,GAAW;IAC5C,OAAOA,IAAIjB,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,MAAMkB,uBAAuB,CAACC,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE,EAAC;AAED,OAAO,MAAMG,0BAA0B,CACrCJ;IAEA,IAAIlC,GAAGuC,qBAAqB,CAACL,OAAO;QAClC,IAAIM,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIP,KAAKQ,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYT,KAAKQ,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAK5C,GAAG6C,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAK5C,GAAG6C,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT,EAAC;AAED,OAAO,MAAMO,cAAc,CAACC;IAC1B,OAAO/C,aAAagD,IAAI,CAACD;AAC3B,EAAC;AACD,OAAO,MAAME,iBAAiB,CAACF;IAC7B,OACE/C,aAAagD,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAACpD,KAAKsD,QAAQ,CAACH;AAE9D,EAAC;AACD,OAAO,MAAMI,aAAa,CAACJ;IACzB,OACE/C,aAAagD,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAACpD,KAAKsD,QAAQ,CAACH;AAErD,EAAC;AAED,uCAAuC;AACvC,OAAO,SAASK,aACd7B,QAAgB,EAChB8B,uBAAiC;IAEjC,MAAMC,SAAShC,UAAUC;IACzB,IAAI+B,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpB3D,GAAG4D,YAAY,CAACJ,QAAS,CAACtB;YACxB,IACElC,GAAG6D,qBAAqB,CAAC3B,SACzBlC,GAAG8D,eAAe,CAAC5B,KAAK6B,UAAU,GAClC;gBACA,IAAI7B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI/B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;wBAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC"}