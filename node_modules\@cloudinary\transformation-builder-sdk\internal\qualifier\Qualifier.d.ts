import { QualifierValue } from './QualifierValue.js';
import { QualifierModel } from '../models/QualifierModel.js';
/**
 * @summary SDK
 * @memberOf SDK
 */
declare class Qualifier extends QualifierModel {
    key: string;
    qualifierValue: QualifierValue;
    delimiter: string;
    constructor(key: string, qualifierValue?: QualifierValue | QualifierValue[] | number | string | (string | number)[]);
    toString(): string;
    addValue(value: any): this;
}
export { Qualifier };
