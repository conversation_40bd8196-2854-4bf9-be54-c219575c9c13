import { IActionModel } from "./models/IActionModel.js";
import { IErrorObject } from "./models/IErrorObject.js";
/**
 * @summary SDK
 * @memberOf SDK
 * @description Defines an action that's a string literal, no validations or manipulations are performed
 */
declare class RawAction {
    readonly raw: string;
    constructor(raw: string);
    toString(): string;
    toJson(): IActionModel | IErrorObject;
}
export { RawAction };
