'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { CustomerSearch } from '@/components/ui/CustomerSearch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { debtService } from '@/lib/services/debtService'
import { productService } from '@/lib/services/productService'
import { formatCurrency } from '@/utils'
import type { DebtTransaction, DebtFormData, Customer, Product } from '@/types'

interface DebtFormProps {
  debt?: DebtTransaction
  onSuccess?: (debt: DebtTransaction) => void
  onCancel?: () => void
  preselectedCustomerId?: string
}

export function DebtForm({ debt, onSuccess, onCancel, preselectedCustomerId }: DebtFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [products, setProducts] = useState<Product[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  
  const [formData, setFormData] = useState<DebtFormData>({
    customer_id: debt?.customer_id || preselectedCustomerId || '',
    product_id: debt?.product_id || '',
    quantity: debt?.quantity || 1,
    notes: debt?.notes || ''
  })

  const [errors, setErrors] = useState<Partial<DebtFormData>>({})

  useEffect(() => {
    loadProducts()
    if (preselectedCustomerId) {
      // Load customer data if preselected
      loadCustomerData(preselectedCustomerId)
    }
  }, [preselectedCustomerId])

  useEffect(() => {
    if (debt) {
      setSelectedCustomer(debt.customer || null)
      setSelectedProduct(debt.product || null)
    }
  }, [debt])

  const loadProducts = async () => {
    try {
      const response = await productService.getProducts({ 
        page: 1, 
        limit: 1000, 
        is_active: true 
      })
      setProducts(response.data)
    } catch (error) {
      console.error('Failed to load products:', error)
    }
  }

  const loadCustomerData = async (customerId: string) => {
    try {
      // This would need to be implemented in customerService
      // For now, we'll handle it in the CustomerSearch component
    } catch (error) {
      console.error('Failed to load customer:', error)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<DebtFormData> = {}

    if (!formData.customer_id) {
      newErrors.customer_id = 'Customer is required'
    }

    if (!formData.product_id) {
      newErrors.product_id = 'Product is required'
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0'
    }

    // Check if product has enough stock
    if (selectedProduct && formData.quantity > selectedProduct.stock_quantity) {
      newErrors.quantity = `Only ${selectedProduct.stock_quantity} items available in stock`
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }))

    // Clear error for this field
    if (errors[name as keyof DebtFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }))
    }

    // Update selected product when product changes
    if (name === 'product_id') {
      const product = products.find(p => p.id === value)
      setSelectedProduct(product || null)
    }
  }

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer)
    setFormData(prev => ({
      ...prev,
      customer_id: customer.id
    }))

    // Clear customer error
    if (errors.customer_id) {
      setErrors(prev => ({
        ...prev,
        customer_id: undefined
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      let savedDebt: DebtTransaction

      if (debt) {
        // Update existing debt (only notes can be updated)
        savedDebt = await debtService.updateDebt(debt.id, { notes: formData.notes })
      } else {
        // Create new debt
        savedDebt = await debtService.createDebt(formData)
      }

      if (onSuccess) {
        onSuccess(savedDebt)
      } else {
        router.push('/debts')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save debt transaction')
      console.error('Debt save error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  const calculateTotal = (): number => {
    if (!selectedProduct || !formData.quantity) return 0
    return selectedProduct.price * formData.quantity
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{debt ? 'Edit Debt Transaction' : 'Record New Debt'}</CardTitle>
        <CardDescription>
          {debt ? 'Update debt transaction details' : 'Record a customer purchase on credit'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer *
            </label>
            {debt ? (
              <div className="p-3 bg-gray-50 rounded-md">
                <div className="font-medium text-gray-900">{debt.customer?.full_name}</div>
                {debt.customer?.phone && (
                  <div className="text-sm text-gray-500">{debt.customer.phone}</div>
                )}
              </div>
            ) : (
              <CustomerSearch
                onSelect={handleCustomerSelect}
                placeholder="Search and select customer..."
                disabled={isLoading}
              />
            )}
            {errors.customer_id && (
              <p className="mt-1 text-sm text-red-600">{errors.customer_id}</p>
            )}
          </div>

          {/* Product Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product *
            </label>
            {debt ? (
              <div className="p-3 bg-gray-50 rounded-md">
                <div className="font-medium text-gray-900">{debt.product_name}</div>
                <div className="text-sm text-gray-500">
                  Price: {formatCurrency(debt.product_price, 'PHP')}
                </div>
              </div>
            ) : (
              <select
                name="product_id"
                value={formData.product_id}
                onChange={handleInputChange}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a product</option>
                {products.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name} - {formatCurrency(product.price, 'PHP')} (Stock: {product.stock_quantity})
                  </option>
                ))}
              </select>
            )}
            {errors.product_id && (
              <p className="mt-1 text-sm text-red-600">{errors.product_id}</p>
            )}
          </div>

          {/* Product Details */}
          {selectedProduct && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Product Details</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 font-medium">Price:</span>
                  <span className="ml-2 text-blue-900">{formatCurrency(selectedProduct.price, 'PHP')}</span>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">Stock:</span>
                  <span className="ml-2 text-blue-900">{selectedProduct.stock_quantity} available</span>
                </div>
                {selectedProduct.net_weight && (
                  <div>
                    <span className="text-blue-700 font-medium">Net Weight:</span>
                    <span className="ml-2 text-blue-900">{selectedProduct.net_weight}</span>
                  </div>
                )}
                <div>
                  <span className="text-blue-700 font-medium">Category:</span>
                  <span className="ml-2 text-blue-900">{selectedProduct.category?.name || 'Uncategorized'}</span>
                </div>
              </div>
            </div>
          )}

          {/* Quantity */}
          <Input
            label="Quantity *"
            name="quantity"
            type="number"
            min="1"
            max={selectedProduct?.stock_quantity || 999}
            value={formData.quantity}
            onChange={handleInputChange}
            error={errors.quantity}
            required
            disabled={isLoading || !!debt}
            placeholder="Enter quantity"
          />

          {/* Total Calculation */}
          {selectedProduct && formData.quantity > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex justify-between items-center">
                <span className="text-green-700 font-medium">Total Amount:</span>
                <span className="text-2xl font-bold text-green-900">
                  {formatCurrency(calculateTotal(), 'PHP')}
                </span>
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Optional notes about this transaction"
              disabled={isLoading}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isLoading}
              disabled={!formData.customer_id || !formData.product_id || !formData.quantity}
            >
              {debt ? 'Update Debt' : 'Record Debt'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
