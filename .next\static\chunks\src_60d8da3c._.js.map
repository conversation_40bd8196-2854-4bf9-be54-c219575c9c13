{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/lib/features/auth/authSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit'\n\ninterface User {\n  id: string\n  email: string\n  name?: string\n}\n\ninterface AuthState {\n  user: User | null\n  isLoading: boolean\n  isAuthenticated: boolean\n}\n\nconst initialState: AuthState = {\n  user: null,\n  isLoading: false,\n  isAuthenticated: false,\n}\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUser: (state, action: PayloadAction<User>) => {\n      state.user = action.payload\n      state.isAuthenticated = true\n      state.isLoading = false\n    },\n    clearUser: (state) => {\n      state.user = null\n      state.isAuthenticated = false\n      state.isLoading = false\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload\n    },\n  },\n})\n\nexport const { setUser, clearUser, setLoading } = authSlice.actions\nexport default authSlice.reducer\n"], "names": [], "mappings": ";;;;;;AAAA;;AAcA,MAAM,eAA0B;IAC9B,MAAM;IACN,WAAW;IACX,iBAAiB;AACnB;AAEA,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG;YACxB,MAAM,SAAS,GAAG;QACpB;QACA,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,SAAS,GAAG;QACpB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;IACF;AACF;AAEO,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,UAAU,OAAO;uCACpD,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/lib/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit'\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'\nimport authSlice from './features/auth/authSlice'\n\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],\n      },\n    }),\n})\n\nexport type RootState = ReturnType<typeof store.getState>\nexport type AppDispatch = typeof store.dispatch\n\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => useDispatch<AppDispatch>()\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,MAAM,8IAAA,CAAA,UAAS;IACjB;IACA,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC;oBAAmB;iBAAoB;YAC1D;QACF;AACJ;AAMO,MAAM,iBAAiB;;IAAM,OAAA,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;AAAe;GAAhD;;QAAuB,4JAAA,CAAA,cAAW;;;AACxC,MAAM,iBAAkD,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/providers/ReduxProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { Provider } from 'react-redux'\nimport { store } from '@/lib/store'\n\ninterface ReduxProviderProps {\n  children: React.ReactNode\n}\n\nexport function ReduxProvider({ children }: ReduxProviderProps) {\n  return <Provider store={store}>{children}</Provider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBAAO,6LAAC,4JAAA,CAAA,WAAQ;QAAC,OAAO,sHAAA,CAAA,QAAK;kBAAG;;;;;;AAClC;KAFgB", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx'\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/utils/cn'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700',\n      outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n      ghost: 'hover:bg-gray-100',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4',\n      lg: 'h-12 px-6 text-lg',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg className=\"mr-2 h-4 w-4 animate-spin\" viewBox=\"0 0 24 24\">\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n              fill=\"none\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBAAI,WAAU;gBAA4B,SAAQ;;kCACjD,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,MAAK;;;;;;kCAEP,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/constants/index.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_NAME = 'Caparan Tindahan'\nexport const APP_DESCRIPTION = 'Professional e-commerce platform'\n\nexport const API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: '/api/auth/login',\n    REGISTER: '/api/auth/register',\n    LOGOUT: '/api/auth/logout',\n    PROFILE: '/api/auth/profile',\n  },\n  USERS: '/api/users',\n  PRODUCTS: '/api/products',\n} as const\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/login',\n  REGISTER: '/register',\n  DASHBOARD: '/dashboard',\n  PROFILE: '/profile',\n  PRODUCTS: '/products',\n} as const\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_LIMIT: 10,\n  MAX_LIMIT: 100,\n} as const\n\nexport const VALIDATION = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n} as const\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;AAEjB,MAAM,WAAW;AACjB,MAAM,kBAAkB;AAExB,MAAM,gBAAgB;IAC3B,MAAM;QACJ,OAAO;QACP,UAAU;QACV,QAAQ;QACR,SAAS;IACX;IACA,OAAO;IACP,UAAU;AACZ;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,cAAc;IACd,eAAe;IACf,WAAW;AACb;AAEO,MAAM,aAAa;IACxB,aAAa;IACb,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;AACnB", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { APP_NAME, ROUTES } from '@/constants'\n\nexport function Header() {\n  return (\n    <header className=\"border-b border-gray-200 bg-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center\">\n            <Link href={ROUTES.HOME} className=\"text-xl font-bold text-gray-900\">\n              {APP_NAME}\n            </Link>\n          </div>\n          \n          <nav className=\"hidden md:flex space-x-8\">\n            <Link href={ROUTES.HOME} className=\"text-gray-700 hover:text-gray-900\">\n              Home\n            </Link>\n            <Link href={ROUTES.PRODUCTS} className=\"text-gray-700 hover:text-gray-900\">\n              Products\n            </Link>\n          </nav>\n          \n          <div className=\"flex items-center space-x-4\">\n            <Link href={ROUTES.LOGIN}>\n              <Button variant=\"ghost\">Login</Button>\n            </Link>\n            <Link href={ROUTES.REGISTER}>\n              <Button>Sign Up</Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,4HAAA,CAAA,SAAM,CAAC,IAAI;4BAAE,WAAU;sCAChC,4HAAA,CAAA,WAAQ;;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,4HAAA,CAAA,SAAM,CAAC,IAAI;gCAAE,WAAU;0CAAoC;;;;;;0CAGvE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,4HAAA,CAAA,SAAM,CAAC,QAAQ;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;kCAK7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,4HAAA,CAAA,SAAM,CAAC,KAAK;0CACtB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAQ;;;;;;;;;;;0CAE1B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,4HAAA,CAAA,SAAM,CAAC,QAAQ;0CACzB,cAAA,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;KAhCgB", "debugId": null}}]}