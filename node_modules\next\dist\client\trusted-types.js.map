{"version": 3, "sources": ["../../src/client/trusted-types.ts"], "sourcesContent": ["/**\n * Stores the Trusted Types Policy. Starts as undefined and can be set to null\n * if Trusted Types is not supported in the browser.\n */\nlet policy: TrustedTypePolicy | null | undefined\n\n/**\n * Getter for the Trusted Types Policy. If it is undefined, it is instantiated\n * here or set to null if Trusted Types is not supported in the browser.\n */\nfunction getPolicy() {\n  if (typeof policy === 'undefined' && typeof window !== 'undefined') {\n    policy =\n      window.trustedTypes?.createPolicy('nextjs', {\n        createHTML: (input) => input,\n        createScript: (input) => input,\n        createScriptURL: (input) => input,\n      }) || null\n  }\n\n  return policy\n}\n\n/**\n * Unsafely promote a string to a TrustedScriptURL, falling back to strings\n * when Trusted Types are not available.\n * This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will cause a browser to load and execute a resource, e.g. when\n * assigning to script.src.\n */\nexport function __unsafeCreateTrustedScriptURL(\n  url: string\n): TrustedScriptURL | string {\n  return getPolicy()?.createScriptURL(url) || url\n}\n"], "names": ["__unsafeCreateTrustedScriptURL", "policy", "getPolicy", "window", "trustedTypes", "createPolicy", "createHTML", "input", "createScript", "createScriptURL", "url"], "mappings": "AAAA;;;CAGC;;;;+BA6BeA;;;eAAAA;;;AA5BhB,IAAIC;AAEJ;;;CAGC,GACD,SAASC;IACP,IAAI,OAAOD,WAAW,eAAe,OAAOE,WAAW,aAAa;YAEhEA;QADFF,SACEE,EAAAA,uBAAAA,OAAOC,YAAY,qBAAnBD,qBAAqBE,YAAY,CAAC,UAAU;YAC1CC,YAAY,CAACC,QAAUA;YACvBC,cAAc,CAACD,QAAUA;YACzBE,iBAAiB,CAACF,QAAUA;QAC9B,OAAM;IACV;IAEA,OAAON;AACT;AAWO,SAASD,+BACdU,GAAW;QAEJR;IAAP,OAAOA,EAAAA,aAAAA,gCAAAA,WAAaO,eAAe,CAACC,SAAQA;AAC9C"}