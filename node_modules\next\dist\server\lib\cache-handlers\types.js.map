{"version": 3, "sources": ["../../../../src/server/lib/cache-handlers/types.ts"], "sourcesContent": ["/**\n * A timestamp in milliseconds elapsed since the epoch\n */\nexport type Timestamp = number\n\nexport interface CacheEntry {\n  /**\n   * The ReadableStream can error and only have partial data so any cache\n   * handlers need to handle this case and decide to keep the partial cache\n   * around or not.\n   */\n  value: ReadableStream<Uint8Array>\n\n  /**\n   * The tags configured for the entry excluding soft tags\n   */\n  tags: string[]\n\n  /**\n   * This is for the client, not used to calculate cache entry expiration\n   * [duration in seconds]\n   */\n  stale: number\n\n  /**\n   * When the cache entry was created [timestamp in milliseconds]\n   */\n  timestamp: Timestamp\n\n  /**\n   * How long the entry is allowed to be used (should be longer than revalidate)\n   * [duration in seconds]\n   */\n  expire: number\n\n  /**\n   * How long until the entry should be revalidated [duration in seconds]\n   */\n  revalidate: number\n}\n\n/**\n * @deprecated Use {@link CacheHandlerV2} instead.\n */\nexport interface CacheHandler {\n  /**\n   * Retrieve a cache entry for the given cache key, if available. The softTags\n   * should be used to check for staleness.\n   */\n  get(cacheKey: string, softTags: string[]): Promise<undefined | CacheEntry>\n\n  /**\n   * Store a cache entry for the given cache key. When this is called, the entry\n   * may still be pending, i.e. its value stream may still be written to. So it\n   * needs to be awaited first. If a `get` for the same cache key is called\n   * before the pending entry is complete, the cache handler must wait for the\n   * `set` operation to finish, before returning the entry, instead of returning\n   * undefined.\n   */\n  set(cacheKey: string, entry: Promise<CacheEntry>): Promise<void>\n\n  /**\n   * Next.js will call this method when `revalidateTag` or `revalidatePath()` is\n   * called. It should update the tags manifest accordingly.\n   */\n  expireTags(...tags: string[]): Promise<void>\n\n  /**\n   * The `receiveExpiredTags` method is called when an action request sends the\n   * 'x-next-revalidated-tags' header to indicate which tags have been expired\n   * by the action. The local tags manifest should be updated accordingly. As\n   * opposed to `expireTags`, the tags don't need to be propagated to a tags\n   * service, as this was already done by the server action.\n   */\n  receiveExpiredTags(...tags: string[]): Promise<void>\n}\n\nexport interface CacheHandlerV2 {\n  /**\n   * Retrieve a cache entry for the given cache key, if available.\n   */\n  get(cacheKey: string): Promise<undefined | CacheEntry>\n\n  /**\n   * Store a cache entry for the given cache key. When this is called, the entry\n   * may still be pending, i.e. its value stream may still be written to. So it\n   * needs to be awaited first. If a `get` for the same cache key is called\n   * before the pending entry is complete, the cache handler must wait for the\n   * `set` operation to finish, before returning the entry, instead of returning\n   * undefined.\n   */\n  set(cacheKey: string, pendingEntry: Promise<CacheEntry>): Promise<void>\n\n  /**\n   * Next.js will call this method periodically, but always before starting a\n   * new request. When working with a remote tags service, this method should\n   * communicate with the tags service to refresh the local tags manifest\n   * accordingly.\n   */\n  refreshTags(): Promise<void>\n\n  /**\n   * Next.js will call this method for each set of soft tags that are relevant\n   * at the start of a request. The result is the maximum timestamp of a\n   * revalidate event for the tags. Returns `0` if none of the tags were ever\n   * revalidated.\n   */\n  getExpiration(...tags: string[]): Promise<Timestamp>\n\n  /**\n   * Next.js will call this method when `revalidateTag` or `revalidatePath()` is\n   * called. It should update the tags manifest accordingly.\n   */\n  expireTags(...tags: string[]): Promise<void>\n}\n\n/**\n * This is a compatibility type to ease migration between cache handler\n * versions. Until the old `CacheHandler` type is removed, this type should be\n * used for all internal Next.js functions that deal with cache handlers to\n * ensure that we are compatible with both cache handler versions. An exception\n * is the built-in default cache handler, which implements the\n * {@link CacheHandlerV2} interface.\n */\nexport type CacheHandlerCompat = CacheHandler | CacheHandlerV2\n"], "names": [], "mappings": "AAAA;;CAEC"}