'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { CustomerSearch } from '@/components/ui/CustomerSearch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { paymentService } from '@/lib/services/paymentService'
import { customerService } from '@/lib/services/customerService'
import { debtService } from '@/lib/services/debtService'
import { useAuth } from '@/components/providers/AuthProvider'
import { formatCurrency } from '@/utils'
import type { Payment, PaymentFormData, Customer, DebtTransaction, CustomerDebtSummary } from '@/types'

interface PaymentFormProps {
  payment?: Payment
  onSuccess?: (payment: Payment) => void
  onCancel?: () => void
  preselectedCustomerId?: string
  preselectedDebtId?: string
}

export function PaymentForm({ 
  payment, 
  onSuccess, 
  onCancel, 
  preselectedCustomerId,
  preselectedDebtId 
}: PaymentFormProps) {
  const router = useRouter()
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerDebtSummary, setCustomerDebtSummary] = useState<CustomerDebtSummary | null>(null)
  const [paymentMethods] = useState(paymentService.getPaymentMethods())
  
  const [formData, setFormData] = useState<PaymentFormData>({
    customer_id: payment?.customer_id || preselectedCustomerId || '',
    amount_paid: payment?.amount_paid || 0,
    payment_method: payment?.payment_method || 'cash',
    notes: payment?.notes || '',
    recorded_by: user?.profile?.full_name || user?.email || ''
  })

  const [errors, setErrors] = useState<Partial<PaymentFormData>>({})

  useEffect(() => {
    if (preselectedCustomerId) {
      loadCustomerData(preselectedCustomerId)
    }
  }, [preselectedCustomerId])

  useEffect(() => {
    if (payment) {
      setSelectedCustomer(payment.customer || null)
      if (payment.customer) {
        loadCustomerDebtSummary(payment.customer.id)
      }
    }
  }, [payment])

  const loadCustomerData = async (customerId: string) => {
    try {
      const customer = await customerService.getCustomer(customerId)
      if (customer) {
        setSelectedCustomer(customer)
        setFormData(prev => ({ ...prev, customer_id: customerId }))
        await loadCustomerDebtSummary(customerId)
      }
    } catch (error) {
      console.error('Failed to load customer:', error)
    }
  }

  const loadCustomerDebtSummary = async (customerId: string) => {
    try {
      const summary = await customerService.getCustomerWithDebts(customerId)
      setCustomerDebtSummary(summary)
    } catch (error) {
      console.error('Failed to load customer debt summary:', error)
    }
  }

  const validateForm = async (): Promise<boolean> => {
    const newErrors: Partial<PaymentFormData> = {}

    if (!formData.customer_id) {
      newErrors.customer_id = 'Customer is required'
    }

    if (!formData.amount_paid || formData.amount_paid <= 0) {
      newErrors.amount_paid = 'Payment amount must be greater than 0'
    }

    if (!formData.payment_method) {
      newErrors.payment_method = 'Payment method is required'
    }

    // Validate payment amount against customer debt
    if (formData.customer_id && formData.amount_paid > 0) {
      const validation = await paymentService.validatePaymentAmount(
        formData.customer_id, 
        formData.amount_paid
      )
      
      if (!validation.isValid) {
        newErrors.amount_paid = validation.error
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }))

    // Clear error for this field
    if (errors[name as keyof PaymentFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }))
    }
  }

  const handleCustomerSelect = async (customer: Customer) => {
    setSelectedCustomer(customer)
    setFormData(prev => ({
      ...prev,
      customer_id: customer.id
    }))

    // Load customer debt summary
    await loadCustomerDebtSummary(customer.id)

    // Clear customer error
    if (errors.customer_id) {
      setErrors(prev => ({
        ...prev,
        customer_id: undefined
      }))
    }
  }

  const handleQuickAmount = (percentage: number) => {
    if (selectedCustomer && selectedCustomer.total_debt > 0) {
      const amount = (selectedCustomer.total_debt * percentage) / 100
      setFormData(prev => ({
        ...prev,
        amount_paid: Math.round(amount * 100) / 100
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!(await validateForm())) {
      return
    }

    setIsLoading(true)

    try {
      let savedPayment: Payment

      if (payment) {
        // Update existing payment (only notes can be updated)
        savedPayment = await paymentService.updatePayment(payment.id, { notes: formData.notes })
      } else {
        // Process new payment
        savedPayment = await paymentService.processPayment(formData)
      }

      if (onSuccess) {
        onSuccess(savedPayment)
      } else {
        router.push('/payments')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to process payment')
      console.error('Payment processing error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{payment ? 'Edit Payment' : 'Process Payment'}</CardTitle>
        <CardDescription>
          {payment ? 'Update payment details' : 'Record a customer payment and update debt balances'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer *
            </label>
            {payment ? (
              <div className="p-3 bg-gray-50 rounded-md">
                <div className="font-medium text-gray-900">{payment.customer?.full_name}</div>
                {payment.customer?.phone && (
                  <div className="text-sm text-gray-500">{payment.customer.phone}</div>
                )}
              </div>
            ) : (
              <CustomerSearch
                onSelect={handleCustomerSelect}
                placeholder="Search and select customer..."
                disabled={isLoading}
                showDebtInfo={true}
              />
            )}
            {errors.customer_id && (
              <p className="mt-1 text-sm text-red-600">{errors.customer_id}</p>
            )}
          </div>

          {/* Customer Debt Summary */}
          {selectedCustomer && (
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-blue-800">Customer Debt Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-blue-700 font-medium">Total Outstanding Debt:</span>
                    <div className="text-2xl font-bold text-blue-900">
                      {formatCurrency(selectedCustomer.total_debt, 'PHP')}
                    </div>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Active Debts:</span>
                    <div className="text-lg font-medium text-blue-900">
                      {customerDebtSummary?.active_debts.length || 0} transactions
                    </div>
                  </div>
                </div>
                
                {selectedCustomer.total_debt > 0 && !payment && (
                  <div>
                    <span className="text-blue-700 font-medium block mb-2">Quick Amount:</span>
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickAmount(25)}
                      >
                        25%
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickAmount(50)}
                      >
                        50%
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickAmount(100)}
                      >
                        Full Amount
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Payment Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Payment Amount (₱) *"
              name="amount_paid"
              type="number"
              step="0.01"
              min="0.01"
              max={selectedCustomer?.total_debt || 999999}
              value={formData.amount_paid}
              onChange={handleInputChange}
              error={errors.amount_paid}
              required
              disabled={isLoading || !!payment}
              placeholder="0.00"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Method *
              </label>
              <select
                name="payment_method"
                value={formData.payment_method}
                onChange={handleInputChange}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading || !!payment}
                required
              >
                {paymentMethods.map(method => (
                  <option key={method.value} value={method.value}>
                    {method.label}
                  </option>
                ))}
              </select>
              {errors.payment_method && (
                <p className="mt-1 text-sm text-red-600">{errors.payment_method}</p>
              )}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Optional notes about this payment"
              disabled={isLoading}
            />
          </div>

          {/* Payment Summary */}
          {selectedCustomer && formData.amount_paid > 0 && (
            <Card className="bg-green-50 border-green-200">
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-green-700">Payment Amount:</span>
                    <span className="font-bold text-green-900">
                      {formatCurrency(formData.amount_paid, 'PHP')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Remaining Debt After Payment:</span>
                    <span className="font-bold text-green-900">
                      {formatCurrency(Math.max(0, selectedCustomer.total_debt - formData.amount_paid), 'PHP')}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isLoading}
              disabled={!formData.customer_id || !formData.amount_paid || !formData.payment_method}
            >
              {payment ? 'Update Payment' : 'Process Payment'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
