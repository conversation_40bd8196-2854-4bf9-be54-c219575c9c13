{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/lib/features/auth/authSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit'\n\ninterface User {\n  id: string\n  email: string\n  name?: string\n}\n\ninterface AuthState {\n  user: User | null\n  isLoading: boolean\n  isAuthenticated: boolean\n}\n\nconst initialState: AuthState = {\n  user: null,\n  isLoading: false,\n  isAuthenticated: false,\n}\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUser: (state, action: PayloadAction<User>) => {\n      state.user = action.payload\n      state.isAuthenticated = true\n      state.isLoading = false\n    },\n    clearUser: (state) => {\n      state.user = null\n      state.isAuthenticated = false\n      state.isLoading = false\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload\n    },\n  },\n})\n\nexport const { setUser, clearUser, setLoading } = authSlice.actions\nexport default authSlice.reducer\n"], "names": [], "mappings": ";;;;;;AAAA;;AAcA,MAAM,eAA0B;IAC9B,MAAM;IACN,WAAW;IACX,iBAAiB;AACnB;AAEA,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG;YACxB,MAAM,SAAS,GAAG;QACpB;QACA,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,SAAS,GAAG;QACpB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;IACF;AACF;AAEO,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,UAAU,OAAO;uCACpD,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/lib/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit'\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'\nimport authSlice from './features/auth/authSlice'\n\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],\n      },\n    }),\n})\n\nexport type RootState = ReturnType<typeof store.getState>\nexport type AppDispatch = typeof store.dispatch\n\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => useDispatch<AppDispatch>()\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,MAAM,8IAAA,CAAA,UAAS;IACjB;IACA,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC;oBAAmB;iBAAoB;YAC1D;QACF;AACJ;AAMO,MAAM,iBAAiB;;IAAM,OAAA,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;AAAe;GAAhD;;QAAuB,4JAAA,CAAA,cAAW;;;AACxC,MAAM,iBAAkD,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/providers/ReduxProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { Provider } from 'react-redux'\nimport { store } from '@/lib/store'\n\ninterface ReduxProviderProps {\n  children: React.ReactNode\n}\n\nexport function ReduxProvider({ children }: ReduxProviderProps) {\n  return <Provider store={store}>{children}</Provider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBAAO,6LAAC,4JAAA,CAAA,WAAQ;QAAC,OAAO,sHAAA,CAAA,QAAK;kBAAG;;;;;;AAClC;KAFgB", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport type { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n)\n\n// Database function helpers\nexport const dbFunctions = {\n  // Process payment for a customer\n  processPayment: async (params: {\n    customer_id: string\n    amount_paid: number\n    payment_method?: string\n    notes?: string\n    recorded_by?: string\n  }) => {\n    const { data, error } = await supabase.rpc('process_payment', params)\n    return { data, error }\n  },\n\n  // Create debt transaction\n  createDebtTransaction: async (params: {\n    customer_id: string\n    product_id: string\n    quantity: number\n    notes?: string\n  }) => {\n    const { data, error } = await supabase.rpc('create_debt_transaction', params)\n    return { data, error }\n  },\n\n  // Get customer debt summary\n  getCustomerDebtSummary: async (customer_id: string) => {\n    const { data, error } = await supabase.rpc('get_customer_debt_summary', {\n      p_customer_id: customer_id\n    })\n    return { data, error }\n  }\n}\n\n// Helper functions for common queries\nexport const queries = {\n  // Get products with category information\n  getProductsWithCategory: () => {\n    return supabase\n      .from('products')\n      .select(`\n        *,\n        category:categories(*)\n      `)\n      .eq('is_active', true)\n      .order('name')\n  },\n\n  // Get customers with debt information\n  getCustomersWithDebt: () => {\n    return supabase\n      .from('customers')\n      .select(`\n        *,\n        debt_transactions(\n          id,\n          remaining_balance,\n          is_fully_paid\n        )\n      `)\n      .eq('is_active', true)\n      .order('family_name')\n  },\n\n  // Get debt transactions with related data\n  getDebtTransactionsWithDetails: () => {\n    return supabase\n      .from('debt_transactions')\n      .select(`\n        *,\n        customer:customers(*),\n        product:products(*)\n      `)\n      .order('debt_date', { ascending: false })\n  },\n\n  // Get payments with related data\n  getPaymentsWithDetails: () => {\n    return supabase\n      .from('payments')\n      .select(`\n        *,\n        customer:customers(*),\n        debt_transaction:debt_transactions(*)\n      `)\n      .order('payment_date', { ascending: false })\n  },\n\n  // Get low stock products\n  getLowStockProducts: (threshold: number = 10) => {\n    return supabase\n      .from('products')\n      .select(`\n        *,\n        category:categories(*)\n      `)\n      .eq('is_active', true)\n      .lte('stock_quantity', threshold)\n      .order('stock_quantity')\n  },\n\n  // Get dashboard statistics\n  getDashboardStats: async () => {\n    const [\n      { count: totalCustomers },\n      { count: totalProducts },\n      { data: totalDebt },\n      { data: todayPayments },\n      { count: lowStockCount },\n      { count: activeDebts }\n    ] = await Promise.all([\n      supabase.from('customers').select('*', { count: 'exact', head: true }).eq('is_active', true),\n      supabase.from('products').select('*', { count: 'exact', head: true }).eq('is_active', true),\n      supabase.from('customers').select('total_debt').eq('is_active', true),\n      supabase.from('payments').select('amount_paid').gte('payment_date', new Date().toISOString().split('T')[0]),\n      supabase.from('products').select('*', { count: 'exact', head: true }).eq('is_active', true).lte('stock_quantity', 10),\n      supabase.from('debt_transactions').select('*', { count: 'exact', head: true }).eq('is_fully_paid', false)\n    ])\n\n    const totalDebtAmount = totalDebt?.reduce((sum, customer) => sum + (customer.total_debt || 0), 0) || 0\n    const totalPaymentsToday = todayPayments?.reduce((sum, payment) => sum + (payment.amount_paid || 0), 0) || 0\n\n    return {\n      total_customers: totalCustomers || 0,\n      total_products: totalProducts || 0,\n      total_debt_amount: totalDebtAmount,\n      total_payments_today: totalPaymentsToday,\n      low_stock_products: lowStockCount || 0,\n      active_debts: activeDebts || 0\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAGoB;AAHpB;;AAGA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACtC,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,EACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAIK,MAAM,cAAc;IACzB,iCAAiC;IACjC,gBAAgB,OAAO;QAOrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,mBAAmB;QAC9D,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,0BAA0B;IAC1B,uBAAuB,OAAO;QAM5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,2BAA2B;QACtE,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,4BAA4B;IAC5B,wBAAwB,OAAO;QAC7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,6BAA6B;YACtE,eAAe;QACjB;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,MAAM,UAAU;IACrB,yCAAyC;IACzC,yBAAyB;QACvB,OAAO,SACJ,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;IACX;IAEA,sCAAsC;IACtC,sBAAsB;QACpB,OAAO,SACJ,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;IACX;IAEA,0CAA0C;IAC1C,gCAAgC;QAC9B,OAAO,SACJ,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM;IAC3C;IAEA,iCAAiC;IACjC,wBAAwB;QACtB,OAAO,SACJ,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,KAAK,CAAC,gBAAgB;YAAE,WAAW;QAAM;IAC9C;IAEA,yBAAyB;IACzB,qBAAqB,CAAC,YAAoB,EAAE;QAC1C,OAAO,SACJ,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,GAAG,CAAC,kBAAkB,WACtB,KAAK,CAAC;IACX;IAEA,2BAA2B;IAC3B,mBAAmB;QACjB,MAAM,CACJ,EAAE,OAAO,cAAc,EAAE,EACzB,EAAE,OAAO,aAAa,EAAE,EACxB,EAAE,MAAM,SAAS,EAAE,EACnB,EAAE,MAAM,aAAa,EAAE,EACvB,EAAE,OAAO,aAAa,EAAE,EACxB,EAAE,OAAO,WAAW,EAAE,CACvB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,EAAE,CAAC,aAAa;YACvF,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,EAAE,CAAC,aAAa;YACtF,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,cAAc,EAAE,CAAC,aAAa;YAChE,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,eAAe,GAAG,CAAC,gBAAgB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1G,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,EAAE,CAAC,aAAa,MAAM,GAAG,CAAC,kBAAkB;YAClH,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,EAAE,CAAC,iBAAiB;SACpG;QAED,MAAM,kBAAkB,WAAW,OAAO,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,UAAU,IAAI,CAAC,GAAG,MAAM;QACrG,MAAM,qBAAqB,eAAe,OAAO,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,WAAW,IAAI,CAAC,GAAG,MAAM;QAE3G,OAAO;YACL,iBAAiB,kBAAkB;YACnC,gBAAgB,iBAAiB;YACjC,mBAAmB;YACnB,sBAAsB;YACtB,oBAAoB,iBAAiB;YACrC,cAAc,eAAe;QAC/B;IACF;AACF", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/providers/AuthProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { supabase } from '@/lib/supabase'\nimport { auth, getAdminProfile, type AuthUser } from '@/lib/auth'\nimport type { User } from '@supabase/supabase-js'\n\ninterface AuthContextType {\n  user: AuthUser | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ user: AuthUser | null; error: string | null }>\n  signUp: (email: string, password: string, fullName: string, role?: string) => Promise<{ user: User | null; error: string | null }>\n  signOut: () => Promise<{ error: string | null }>\n  resetPassword: (email: string) => Promise<{ error: string | null }>\n  updatePassword: (password: string) => Promise<{ error: string | null }>\n  updateProfile: (updates: any) => Promise<{ error: string | null }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function Auth<PERSON>rovider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<AuthUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session } } = await supabase.auth.getSession()\n        \n        if (session?.user) {\n          const profile = await getAdminProfile(session.user.id)\n          setUser({ ...session.user, profile } as AuthUser)\n        }\n      } catch (error) {\n        console.error('Error getting initial session:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        try {\n          if (session?.user) {\n            const profile = await getAdminProfile(session.user.id)\n            setUser({ ...session.user, profile } as AuthUser)\n          } else {\n            setUser(null)\n          }\n        } catch (error) {\n          console.error('Error in auth state change:', error)\n          setUser(null)\n        } finally {\n          setLoading(false)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    setLoading(true)\n    try {\n      const result = await auth.signIn(email, password)\n      if (result.user) {\n        setUser(result.user)\n      }\n      return result\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string, role?: string) => {\n    setLoading(true)\n    try {\n      return await auth.signUp(email, password, fullName, role)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    setLoading(true)\n    try {\n      const result = await auth.signOut()\n      setUser(null)\n      return result\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    return await auth.resetPassword(email)\n  }\n\n  const updatePassword = async (password: string) => {\n    return await auth.updatePassword(password)\n  }\n\n  const updateProfile = async (updates: any) => {\n    const result = await auth.updateProfile(updates)\n    \n    // Refresh user data if update was successful\n    if (!result.error && user) {\n      const profile = await getAdminProfile(user.id)\n      setUser({ ...user, profile } as AuthUser)\n    }\n    \n    return result\n  }\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n    updatePassword,\n    updateProfile\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Hook for checking permissions\nexport function usePermissions() {\n  const { user } = useAuth()\n\n  const hasRole = (requiredRole: string | string[]): boolean => {\n    if (!user?.profile) return false\n    \n    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]\n    return roles.includes(user.profile.role)\n  }\n\n  const canPerformAction = (action: string): boolean => {\n    if (!user?.profile) return false\n\n    const { role } = user.profile\n\n    const permissions = {\n      super_admin: [\n        'create_product', 'edit_product', 'delete_product',\n        'create_customer', 'edit_customer', 'delete_customer',\n        'create_debt', 'edit_debt', 'delete_debt',\n        'process_payment', 'edit_payment', 'delete_payment',\n        'view_analytics', 'manage_users', 'system_settings'\n      ],\n      admin: [\n        'create_product', 'edit_product',\n        'create_customer', 'edit_customer',\n        'create_debt', 'edit_debt',\n        'process_payment', 'edit_payment',\n        'view_analytics'\n      ],\n      cashier: [\n        'create_debt', 'process_payment', 'view_analytics'\n      ]\n    }\n\n    return permissions[role]?.includes(action) || false\n  }\n\n  const isSuperAdmin = (): boolean => hasRole('super_admin')\n  const isAdmin = (): boolean => hasRole(['super_admin', 'admin'])\n  const isCashier = (): boolean => hasRole('cashier')\n\n  return {\n    user,\n    hasRole,\n    canPerformAction,\n    isSuperAdmin,\n    isAdmin,\n    isCashier\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;wBAE5D,IAAI,SAAS,MAAM;4BACjB,MAAM,UAAU,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,IAAI,CAAC,EAAE;4BACrD,QAAQ;gCAAE,GAAG,QAAQ,IAAI;gCAAE;4BAAQ;wBACrC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,IAAI;wBACF,IAAI,SAAS,MAAM;4BACjB,MAAM,UAAU,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,IAAI,CAAC,EAAE;4BACrD,QAAQ;gCAAE,GAAG,QAAQ,IAAI;gCAAE;4BAAQ;wBACrC,OAAO;4BACL,QAAQ;wBACV;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC7C,QAAQ;oBACV,SAAU;wBACR,WAAW;oBACb;gBACF;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,qHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;YACxC,IAAI,OAAO,IAAI,EAAE;gBACf,QAAQ,OAAO,IAAI;YACrB;YACA,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB,UAAkB;QACvE,WAAW;QACX,IAAI;YACF,OAAO,MAAM,qHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO,UAAU,UAAU;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,qHAAA,CAAA,OAAI,CAAC,OAAO;YACjC,QAAQ;YACR,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,OAAO,MAAM,qHAAA,CAAA,OAAI,CAAC,aAAa,CAAC;IAClC;IAEA,MAAM,iBAAiB,OAAO;QAC5B,OAAO,MAAM,qHAAA,CAAA,OAAI,CAAC,cAAc,CAAC;IACnC;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,SAAS,MAAM,qHAAA,CAAA,OAAI,CAAC,aAAa,CAAC;QAExC,6CAA6C;QAC7C,IAAI,CAAC,OAAO,KAAK,IAAI,MAAM;YACzB,MAAM,UAAU,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YAC7C,QAAQ;gBAAE,GAAG,IAAI;gBAAE;YAAQ;QAC7B;QAEA,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAlHgB;KAAA;AAoHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,SAAS,OAAO;QAE3B,MAAM,QAAQ,MAAM,OAAO,CAAC,gBAAgB,eAAe;YAAC;SAAa;QACzE,OAAO,MAAM,QAAQ,CAAC,KAAK,OAAO,CAAC,IAAI;IACzC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM,SAAS,OAAO;QAE3B,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,OAAO;QAE7B,MAAM,cAAc;YAClB,aAAa;gBACX;gBAAkB;gBAAgB;gBAClC;gBAAmB;gBAAiB;gBACpC;gBAAe;gBAAa;gBAC5B;gBAAmB;gBAAgB;gBACnC;gBAAkB;gBAAgB;aACnC;YACD,OAAO;gBACL;gBAAkB;gBAClB;gBAAmB;gBACnB;gBAAe;gBACf;gBAAmB;gBACnB;aACD;YACD,SAAS;gBACP;gBAAe;gBAAmB;aACnC;QACH;QAEA,OAAO,WAAW,CAAC,KAAK,EAAE,SAAS,WAAW;IAChD;IAEA,MAAM,eAAe,IAAe,QAAQ;IAC5C,MAAM,UAAU,IAAe,QAAQ;YAAC;YAAe;SAAQ;IAC/D,MAAM,YAAY,IAAe,QAAQ;IAEzC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAlDgB;;QACG", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx'\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/utils/cn'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700',\n      outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n      ghost: 'hover:bg-gray-100',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4',\n      lg: 'h-12 px-6 text-lg',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg className=\"mr-2 h-4 w-4 animate-spin\" viewBox=\"0 0 24 24\">\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n              fill=\"none\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBAAI,WAAU;gBAA4B,SAAQ;;kCACjD,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,MAAK;;;;;;kCAEP,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/constants/index.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_NAME = 'Caparan Tindahan'\nexport const APP_DESCRIPTION = 'Professional e-commerce platform'\n\nexport const API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: '/api/auth/login',\n    REGISTER: '/api/auth/register',\n    LOGOUT: '/api/auth/logout',\n    PROFILE: '/api/auth/profile',\n  },\n  USERS: '/api/users',\n  PRODUCTS: '/api/products',\n} as const\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/login',\n  REGISTER: '/register',\n  DASHBOARD: '/dashboard',\n  PROFILE: '/profile',\n  PRODUCTS: '/products',\n} as const\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_LIMIT: 10,\n  MAX_LIMIT: 100,\n} as const\n\nexport const VALIDATION = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n} as const\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;AAEjB,MAAM,WAAW;AACjB,MAAM,kBAAkB;AAExB,MAAM,gBAAgB;IAC3B,MAAM;QACJ,OAAO;QACP,UAAU;QACV,QAAQ;QACR,SAAS;IACX;IACA,OAAO;IACP,UAAU;AACZ;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,cAAc;IACd,eAAe;IACf,WAAW;AACb;AAEO,MAAM,aAAa;IACxB,aAAa;IACb,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;AACnB", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/caparan-tindahan/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useAuth } from '@/components/providers/AuthProvider'\nimport { APP_NAME } from '@/constants'\n\nexport function Header() {\n  const { user, signOut } = useAuth()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      router.push('/auth/login')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <header className=\"border-b border-gray-200 bg-white shadow-sm\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center\">\n            <Link href={user ? \"/dashboard\" : \"/\"} className=\"text-xl font-bold text-gray-900\">\n              {APP_NAME}\n            </Link>\n            {user && (\n              <span className=\"ml-2 text-sm text-gray-500\">Admin Dashboard</span>\n            )}\n          </div>\n\n          {user && (\n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-gray-900\">\n                Dashboard\n              </Link>\n              <Link href=\"/products\" className=\"text-gray-700 hover:text-gray-900\">\n                Products\n              </Link>\n              <Link href=\"/customers\" className=\"text-gray-700 hover:text-gray-900\">\n                Customers\n              </Link>\n              <Link href=\"/debts\" className=\"text-gray-700 hover:text-gray-900\">\n                Debts\n              </Link>\n              <Link href=\"/payments\" className=\"text-gray-700 hover:text-gray-900\">\n                Payments\n              </Link>\n            </nav>\n          )}\n\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-sm\">\n                  <p className=\"font-medium text-gray-900\">\n                    {user.profile?.full_name || user.email}\n                  </p>\n                  <p className=\"text-gray-500 capitalize\">\n                    {user.profile?.role}\n                  </p>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  onClick={handleSignOut}\n                  isLoading={isLoading}\n                >\n                  Sign Out\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/auth/login\">\n                  <Button variant=\"ghost\">Login</Button>\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,OAAO,eAAe;gCAAK,WAAU;0CAC9C,4HAAA,CAAA,WAAQ;;;;;;4BAEV,sBACC,6LAAC;gCAAK,WAAU;0CAA6B;;;;;;;;;;;;oBAIhD,sBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAoC;;;;;;0CAGtE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAoC;;;;;;0CAGrE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAoC;;;;;;0CAGtE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAoC;;;;;;0CAGlE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAoC;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;kCACZ,qBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,KAAK,OAAO,EAAE,aAAa,KAAK,KAAK;;;;;;sDAExC,6LAAC;4CAAE,WAAU;sDACV,KAAK,OAAO,EAAE;;;;;;;;;;;;8CAGnB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAW;8CACZ;;;;;;;;;;;iDAKH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;GAjFgB;;QACY,kJAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}]}