{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/components/shadow-portal.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { createPortal } from 'react-dom'\nimport { STORAGE_KEY_THEME } from '../../shared'\n\nexport function ShadowPortal({ children }: { children: React.ReactNode }) {\n  let portalNode = React.useRef<HTMLElement | null>(null)\n  let shadowNode = React.useRef<ShadowRoot | null>(null)\n  let [, forceUpdate] = React.useState<{} | undefined>()\n\n  // Don't use useLayoutEffect here, as it will cause warnings during SSR in React 18.\n  // Don't use useSyncExternalStore as an SSR gate unless you verified it doesn't\n  // downgrade a Transition of the initial root render to a sync render or\n  // we can assure the root render is not a Transition.\n  React.useEffect(() => {\n    const ownerDocument = document\n    portalNode.current = ownerDocument.createElement('nextjs-portal')\n    // load default color preference from localstorage\n    if (typeof localStorage !== 'undefined') {\n      const theme = localStorage.getItem(STORAGE_KEY_THEME)\n      if (theme === 'dark') {\n        portalNode.current.classList.add('dark')\n        portalNode.current.classList.remove('light')\n      } else if (theme === 'light') {\n        portalNode.current.classList.remove('dark')\n        portalNode.current.classList.add('light')\n      }\n    }\n\n    shadowNode.current = portalNode.current.attachShadow({ mode: 'open' })\n    ownerDocument.body.appendChild(portalNode.current)\n    forceUpdate({})\n    return () => {\n      if (portalNode.current && portalNode.current.ownerDocument) {\n        portalNode.current.ownerDocument.body.removeChild(portalNode.current)\n      }\n    }\n  }, [])\n\n  return shadowNode.current\n    ? createPortal(children, shadowNode.current as any)\n    : null\n}\n"], "names": ["React", "createPortal", "STORAGE_KEY_THEME", "ShadowPort<PERSON>", "children", "portalNode", "useRef", "shadowNode", "forceUpdate", "useState", "useEffect", "ownerDocument", "document", "current", "createElement", "localStorage", "theme", "getItem", "classList", "add", "remove", "attachShadow", "mode", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,YAAY,QAAQ,YAAW;AACxC,SAASC,iBAAiB,QAAQ,eAAc;AAEhD,OAAO,SAASC,aAAa,KAA2C;IAA3C,IAAA,EAAEC,QAAQ,EAAiC,GAA3C;IAC3B,IAAIC,aAAaL,MAAMM,MAAM,CAAqB;IAClD,IAAIC,aAAaP,MAAMM,MAAM,CAAoB;IACjD,IAAI,GAAGE,YAAY,GAAGR,MAAMS,QAAQ;IAEpC,oFAAoF;IACpF,+EAA+E;IAC/E,wEAAwE;IACxE,qDAAqD;IACrDT,MAAMU,SAAS,CAAC;QACd,MAAMC,gBAAgBC;QACtBP,WAAWQ,OAAO,GAAGF,cAAcG,aAAa,CAAC;QACjD,kDAAkD;QAClD,IAAI,OAAOC,iBAAiB,aAAa;YACvC,MAAMC,QAAQD,aAAaE,OAAO,CAACf;YACnC,IAAIc,UAAU,QAAQ;gBACpBX,WAAWQ,OAAO,CAACK,SAAS,CAACC,GAAG,CAAC;gBACjCd,WAAWQ,OAAO,CAACK,SAAS,CAACE,MAAM,CAAC;YACtC,OAAO,IAAIJ,UAAU,SAAS;gBAC5BX,WAAWQ,OAAO,CAACK,SAAS,CAACE,MAAM,CAAC;gBACpCf,WAAWQ,OAAO,CAACK,SAAS,CAACC,GAAG,CAAC;YACnC;QACF;QAEAZ,WAAWM,OAAO,GAAGR,WAAWQ,OAAO,CAACQ,YAAY,CAAC;YAAEC,MAAM;QAAO;QACpEX,cAAcY,IAAI,CAACC,WAAW,CAACnB,WAAWQ,OAAO;QACjDL,YAAY,CAAC;QACb,OAAO;YACL,IAAIH,WAAWQ,OAAO,IAAIR,WAAWQ,OAAO,CAACF,aAAa,EAAE;gBAC1DN,WAAWQ,OAAO,CAACF,aAAa,CAACY,IAAI,CAACE,WAAW,CAACpB,WAAWQ,OAAO;YACtE;QACF;IACF,GAAG,EAAE;IAEL,OAAON,WAAWM,OAAO,iBACrBZ,aAAaG,UAAUG,WAAWM,OAAO,IACzC;AACN"}