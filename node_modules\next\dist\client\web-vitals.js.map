{"version": 3, "sources": ["../../src/client/web-vitals.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport {\n  onLCP,\n  onFID,\n  onCLS,\n  onINP,\n  onFCP,\n  onTTFB,\n} from 'next/dist/compiled/web-vitals'\nimport type { Metric } from 'next/dist/compiled/web-vitals'\n\nexport function useReportWebVitals(\n  reportWebVitalsFn: (metric: Metric) => void\n) {\n  useEffect(() => {\n    onCLS(reportWebVitalsFn)\n    onFID(reportWebVitalsFn)\n    onLCP(reportWebVitalsFn)\n    onINP(reportWebVitalsFn)\n    onFCP(reportWebVitalsFn)\n    onTTFB(reportWebVitalsFn)\n  }, [reportWebVitalsFn])\n}\n"], "names": ["useReportWebVitals", "reportWebVitalsFn", "useEffect", "onCLS", "onFID", "onLCP", "onINP", "onFCP", "onTTFB"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;uBAXU;2BAQnB;AAGA,SAASA,mBACdC,iBAA2C;IAE3CC,IAAAA,gBAAS,EAAC;QACRC,IAAAA,gBAAK,EAACF;QACNG,IAAAA,gBAAK,EAACH;QACNI,IAAAA,gBAAK,EAACJ;QACNK,IAAAA,gBAAK,EAACL;QACNM,IAAAA,gBAAK,EAACN;QACNO,IAAAA,iBAAM,EAACP;IACT,GAAG;QAACA;KAAkB;AACxB"}