/**
 * @memberOf Qualifiers
 * @description This namespace contains all the colors used in the SDK
 * @namespace Color
 * @example
 * // Reference only, do NOT use within your code for tree-shaking reasons
 * // SDK functions that require color accept a string (like 'red') or a hex value, like 'ffffff'
 * import {Color} from '@cloudinary/url-gen/qualifiers/color'
 * console.log(Color.RED);
 */
declare const Color: {
    SNOW: string;
    SNOW1: string;
    SNOW2: string;
    ROSYBROWN1: string;
    ROSYBROWN2: string;
    SNOW3: string;
    LIGHTCORAL: string;
    INDIANRED1: string;
    ROSYBROWN3: string;
    INDIANRED2: string;
    ROSYBROWN: string;
    BROWN1: string;
    FIREBRICK1: string;
    BROWN2: string;
    INDIANRED: string;
    INDIANRED3: string;
    FIREBRICK2: string;
    SNOW4: string;
    BROWN3: string;
    RED: string;
    RED1: string;
    ROSYBROWN4: string;
    FIREBRICK3: string;
    RED2: string;
    FIREBRICK: string;
    BROWN: string;
    RED3: string;
    INDIANRED4: string;
    BROWN4: string;
    FIREBRICK4: string;
    DARKRED: string;
    RED4: string;
    LIGHTPINK1: string;
    LIGHTPINK3: string;
    LIGHTPINK4: string;
    LIGHTPINK2: string;
    LIGHTPINK: string;
    PINK: string;
    CRIMSON: string;
    PINK1: string;
    PINK2: string;
    PINK3: string;
    PINK4: string;
    PALEVIOLETRED4: string;
    PALEVIOLETRED: string;
    PALEVIOLETRED2: string;
    PALEVIOLETRED1: string;
    PALEVIOLETRED3: string;
    LAVENDERBLUSH: string;
    LAVENDERBLUSH1: string;
    LAVENDERBLUSH3: string;
    LAVENDERBLUSH2: string;
    LAVENDERBLUSH4: string;
    MAROON: string;
    HOTPINK3: string;
    VIOLETRED3: string;
    VIOLETRED1: string;
    VIOLETRED2: string;
    VIOLETRED4: string;
    HOTPINK2: string;
    HOTPINK1: string;
    HOTPINK4: string;
    HOTPINK: string;
    DEEPPINK: string;
    DEEPPINK1: string;
    DEEPPINK2: string;
    DEEPPINK3: string;
    DEEPPINK4: string;
    MAROON1: string;
    MAROON2: string;
    MAROON3: string;
    MAROON4: string;
    MEDIUMVIOLETRED: string;
    VIOLETRED: string;
    ORCHID2: string;
    ORCHID: string;
    ORCHID1: string;
    ORCHID3: string;
    ORCHID4: string;
    THISTLE1: string;
    THISTLE2: string;
    PLUM1: string;
    PLUM2: string;
    THISTLE: string;
    THISTLE3: string;
    PLUM: string;
    VIOLET: string;
    PLUM3: string;
    THISTLE4: string;
    FUCHSIA: string;
    MAGENTA: string;
    MAGENTA1: string;
    PLUM4: string;
    MAGENTA2: string;
    MAGENTA3: string;
    DARKMAGENTA: string;
    MAGENTA4: string;
    PURPLE: string;
    MEDIUMORCHID: string;
    MEDIUMORCHID1: string;
    MEDIUMORCHID2: string;
    MEDIUMORCHID3: string;
    MEDIUMORCHID4: string;
    DARKVIOLET: string;
    DARKORCHID: string;
    DARKORCHID1: string;
    DARKORCHID3: string;
    DARKORCHID2: string;
    DARKORCHID4: string;
    INDIGO: string;
    BLUEVIOLET: string;
    PURPLE2: string;
    PURPLE3: string;
    PURPLE4: string;
    PURPLE1: string;
    MEDIUMPURPLE: string;
    MEDIUMPURPLE1: string;
    MEDIUMPURPLE2: string;
    MEDIUMPURPLE3: string;
    MEDIUMPURPLE4: string;
    DARKSLATEBLUE: string;
    LIGHTSLATEBLUE: string;
    MEDIUMSLATEBLUE: string;
    SLATEBLUE: string;
    SLATEBLUE1: string;
    SLATEBLUE2: string;
    SLATEBLUE3: string;
    SLATEBLUE4: string;
    GHOSTWHITE: string;
    LAVENDER: string;
    BLUE: string;
    BLUE1: string;
    BLUE2: string;
    BLUE3: string;
    MEDIUMBLUE: string;
    BLUE4: string;
    DARKBLUE: string;
    MIDNIGHTBLUE: string;
    NAVY: string;
    NAVYBLUE: string;
    ROYALBLUE: string;
    ROYALBLUE1: string;
    ROYALBLUE2: string;
    ROYALBLUE3: string;
    ROYALBLUE4: string;
    CORNFLOWERBLUE: string;
    LIGHTSTEELBLUE: string;
    LIGHTSTEELBLUE1: string;
    LIGHTSTEELBLUE2: string;
    LIGHTSTEELBLUE3: string;
    LIGHTSTEELBLUE4: string;
    SLATEGRAY4: string;
    SLATEGRAY1: string;
    SLATEGRAY2: string;
    SLATEGRAY3: string;
    LIGHTSLATEGRAY: string;
    LIGHTSLATEGREY: string;
    SLATEGRAY: string;
    SLATEGREY: string;
    DODGERBLUE: string;
    DODGERBLUE1: string;
    DODGERBLUE2: string;
    DODGERBLUE4: string;
    DODGERBLUE3: string;
    ALICEBLUE: string;
    STEELBLUE4: string;
    STEELBLUE: string;
    STEELBLUE1: string;
    STEELBLUE2: string;
    STEELBLUE3: string;
    SKYBLUE4: string;
    SKYBLUE1: string;
    SKYBLUE2: string;
    SKYBLUE3: string;
    LIGHTSKYBLUE: string;
    LIGHTSKYBLUE4: string;
    LIGHTSKYBLUE1: string;
    LIGHTSKYBLUE2: string;
    LIGHTSKYBLUE3: string;
    SKYBLUE: string;
    LIGHTBLUE3: string;
    DEEPSKYBLUE: string;
    DEEPSKYBLUE1: string;
    DEEPSKYBLUE2: string;
    DEEPSKYBLUE4: string;
    DEEPSKYBLUE3: string;
    LIGHTBLUE1: string;
    LIGHTBLUE2: string;
    LIGHTBLUE: string;
    LIGHTBLUE4: string;
    POWDERBLUE: string;
    CADETBLUE1: string;
    CADETBLUE2: string;
    CADETBLUE3: string;
    CADETBLUE4: string;
    TURQUOISE1: string;
    TURQUOISE2: string;
    TURQUOISE3: string;
    TURQUOISE4: string;
    CADETBLUE: string;
    DARKTURQUOISE: string;
    AZURE: string;
    AZURE1: string;
    LIGHTCYAN1: string;
    LIGHTCYAN: string;
    AZURE2: string;
    LIGHTCYAN2: string;
    PALETURQUOISE1: string;
    PALETURQUOISE: string;
    PALETURQUOISE2: string;
    DARKSLATEGRAY1: string;
    AZURE3: string;
    LIGHTCYAN3: string;
    DARKSLATEGRAY2: string;
    PALETURQUOISE3: string;
    DARKSLATEGRAY3: string;
    AZURE4: string;
    LIGHTCYAN4: string;
    AQUA: string;
    CYAN: string;
    CYAN1: string;
    PALETURQUOISE4: string;
    CYAN2: string;
    DARKSLATEGRAY4: string;
    CYAN3: string;
    CYAN4: string;
    DARKCYAN: string;
    TEAL: string;
    DARKSLATEGRAY: string;
    DARKSLATEGREY: string;
    MEDIUMTURQUOISE: string;
    LIGHTSEAGREEN: string;
    TURQUOISE: string;
    AQUAMARINE4: string;
    AQUAMARINE: string;
    AQUAMARINE1: string;
    AQUAMARINE2: string;
    AQUAMARINE3: string;
    MEDIUMAQUAMARINE: string;
    MEDIUMSPRINGGREEN: string;
    MINTCREAM: string;
    SPRINGGREEN: string;
    SPRINGGREEN1: string;
    SPRINGGREEN2: string;
    SPRINGGREEN3: string;
    SPRINGGREEN4: string;
    MEDIUMSEAGREEN: string;
    SEAGREEN: string;
    SEAGREEN3: string;
    SEAGREEN1: string;
    SEAGREEN4: string;
    SEAGREEN2: string;
    MEDIUMFORESTGREEN: string;
    HONEYDEW: string;
    HONEYDEW1: string;
    HONEYDEW2: string;
    DARKSEAGREEN1: string;
    DARKSEAGREEN2: string;
    PALEGREEN1: string;
    PALEGREEN: string;
    HONEYDEW3: string;
    LIGHTGREEN: string;
    PALEGREEN2: string;
    DARKSEAGREEN3: string;
    DARKSEAGREEN: string;
    PALEGREEN3: string;
    HONEYDEW4: string;
    GREEN1: string;
    LIME: string;
    LIMEGREEN: string;
    DARKSEAGREEN4: string;
    GREEN2: string;
    PALEGREEN4: string;
    GREEN3: string;
    FORESTGREEN: string;
    GREEN4: string;
    GREEN: string;
    DARKGREEN: string;
    LAWNGREEN: string;
    CHARTREUSE: string;
    CHARTREUSE1: string;
    CHARTREUSE2: string;
    CHARTREUSE3: string;
    CHARTREUSE4: string;
    GREENYELLOW: string;
    DARKOLIVEGREEN3: string;
    DARKOLIVEGREEN1: string;
    DARKOLIVEGREEN2: string;
    DARKOLIVEGREEN4: string;
    DARKOLIVEGREEN: string;
    OLIVEDRAB: string;
    OLIVEDRAB1: string;
    OLIVEDRAB2: string;
    OLIVEDRAB3: string;
    YELLOWGREEN: string;
    OLIVEDRAB4: string;
    IVORY: string;
    IVORY1: string;
    LIGHTYELLOW: string;
    LIGHTYELLOW1: string;
    BEIGE: string;
    IVORY2: string;
    LIGHTGOLDENRODYELLOW: string;
    LIGHTYELLOW2: string;
    IVORY3: string;
    LIGHTYELLOW3: string;
    IVORY4: string;
    LIGHTYELLOW4: string;
    YELLOW: string;
    YELLOW1: string;
    YELLOW2: string;
    YELLOW3: string;
    YELLOW4: string;
    OLIVE: string;
    DARKKHAKI: string;
    KHAKI2: string;
    LEMONCHIFFON4: string;
    KHAKI1: string;
    KHAKI3: string;
    KHAKI4: string;
    PALEGOLDENROD: string;
    LEMONCHIFFON: string;
    LEMONCHIFFON1: string;
    KHAKI: string;
    LEMONCHIFFON3: string;
    LEMONCHIFFON2: string;
    MEDIUMGOLDENROD: string;
    CORNSILK4: string;
    GOLD: string;
    GOLD1: string;
    GOLD2: string;
    GOLD3: string;
    GOLD4: string;
    LIGHTGOLDENROD: string;
    LIGHTGOLDENROD4: string;
    LIGHTGOLDENROD1: string;
    LIGHTGOLDENROD3: string;
    LIGHTGOLDENROD2: string;
    CORNSILK3: string;
    CORNSILK2: string;
    CORNSILK: string;
    CORNSILK1: string;
    GOLDENROD: string;
    GOLDENROD1: string;
    GOLDENROD2: string;
    GOLDENROD3: string;
    GOLDENROD4: string;
    DARKGOLDENROD: string;
    DARKGOLDENROD1: string;
    DARKGOLDENROD2: string;
    DARKGOLDENROD3: string;
    DARKGOLDENROD4: string;
    FLORALWHITE: string;
    WHEAT2: string;
    OLDLACE: string;
    WHEAT: string;
    WHEAT1: string;
    WHEAT3: string;
    ORANGE: string;
    ORANGE1: string;
    ORANGE2: string;
    ORANGE3: string;
    ORANGE4: string;
    WHEAT4: string;
    MOCCASIN: string;
    PAPAYAWHIP: string;
    NAVAJOWHITE3: string;
    BLANCHEDALMOND: string;
    NAVAJOWHITE: string;
    NAVAJOWHITE1: string;
    NAVAJOWHITE2: string;
    NAVAJOWHITE4: string;
    ANTIQUEWHITE4: string;
    ANTIQUEWHITE: string;
    TAN: string;
    BISQUE4: string;
    BURLYWOOD: string;
    ANTIQUEWHITE2: string;
    BURLYWOOD1: string;
    BURLYWOOD3: string;
    BURLYWOOD2: string;
    ANTIQUEWHITE1: string;
    BURLYWOOD4: string;
    ANTIQUEWHITE3: string;
    DARKORANGE: string;
    BISQUE2: string;
    BISQUE: string;
    BISQUE1: string;
    BISQUE3: string;
    DARKORANGE1: string;
    LINEN: string;
    DARKORANGE2: string;
    DARKORANGE3: string;
    DARKORANGE4: string;
    PERU: string;
    TAN1: string;
    TAN2: string;
    TAN3: string;
    TAN4: string;
    PEACHPUFF: string;
    PEACHPUFF1: string;
    PEACHPUFF4: string;
    PEACHPUFF2: string;
    PEACHPUFF3: string;
    SANDYBROWN: string;
    SEASHELL4: string;
    SEASHELL2: string;
    SEASHELL3: string;
    CHOCOLATE: string;
    CHOCOLATE1: string;
    CHOCOLATE2: string;
    CHOCOLATE3: string;
    CHOCOLATE4: string;
    SADDLEBROWN: string;
    SEASHELL: string;
    SEASHELL1: string;
    SIENNA4: string;
    SIENNA: string;
    SIENNA1: string;
    SIENNA2: string;
    SIENNA3: string;
    LIGHTSALMON3: string;
    LIGHTSALMON: string;
    LIGHTSALMON1: string;
    LIGHTSALMON4: string;
    LIGHTSALMON2: string;
    CORAL: string;
    ORANGERED: string;
    ORANGERED1: string;
    ORANGERED2: string;
    ORANGERED3: string;
    ORANGERED4: string;
    DARKSALMON: string;
    SALMON1: string;
    SALMON2: string;
    SALMON3: string;
    SALMON4: string;
    CORAL1: string;
    CORAL2: string;
    CORAL3: string;
    CORAL4: string;
    TOMATO4: string;
    TOMATO: string;
    TOMATO1: string;
    TOMATO2: string;
    TOMATO3: string;
    MISTYROSE4: string;
    MISTYROSE2: string;
    MISTYROSE: string;
    MISTYROSE1: string;
    SALMON: string;
    MISTYROSE3: string;
    WHITE: string;
    GRAY100: string;
    GREY100: string;
    GRAY99: string;
    GREY99: string;
    GRAY98: string;
    GREY98: string;
    GRAY97: string;
    GREY97: string;
    GRAY96: string;
    GREY96: string;
    WHITESMOKE: string;
    GRAY95: string;
    GREY95: string;
    GRAY94: string;
    GREY94: string;
    GRAY93: string;
    GREY93: string;
    GRAY92: string;
    GREY92: string;
    GRAY91: string;
    GREY91: string;
    GRAY90: string;
    GREY90: string;
    GRAY89: string;
    GREY89: string;
    GRAY88: string;
    GREY88: string;
    GRAY87: string;
    GREY87: string;
    GAINSBORO: string;
    GRAY86: string;
    GREY86: string;
    GRAY85: string;
    GREY85: string;
    GRAY84: string;
    GREY84: string;
    GRAY83: string;
    GREY83: string;
    LIGHTGRAY: string;
    LIGHTGREY: string;
    GRAY82: string;
    GREY82: string;
    GRAY81: string;
    GREY81: string;
    GRAY80: string;
    GREY80: string;
    GRAY79: string;
    GREY79: string;
    GRAY78: string;
    GREY78: string;
    GRAY77: string;
    GREY77: string;
    GRAY76: string;
    GREY76: string;
    SILVER: string;
    GRAY75: string;
    GREY75: string;
    GRAY74: string;
    GREY74: string;
    GRAY73: string;
    GREY73: string;
    GRAY72: string;
    GREY72: string;
    GRAY71: string;
    GREY71: string;
    GRAY70: string;
    GREY70: string;
    GRAY69: string;
    GREY69: string;
    GRAY68: string;
    GREY68: string;
    GRAY67: string;
    GREY67: string;
    DARKGRAY: string;
    DARKGREY: string;
    GRAY66: string;
    GREY66: string;
    GRAY65: string;
    GREY65: string;
    GRAY64: string;
    GREY64: string;
    GRAY63: string;
    GREY63: string;
    GRAY62: string;
    GREY62: string;
    GRAY61: string;
    GREY61: string;
    GRAY60: string;
    GREY60: string;
    GRAY59: string;
    GREY59: string;
    GRAY58: string;
    GREY58: string;
    GRAY57: string;
    GREY57: string;
    GRAY56: string;
    GREY56: string;
    GRAY55: string;
    GREY55: string;
    GRAY54: string;
    GREY54: string;
    GRAY53: string;
    GREY53: string;
    GRAY52: string;
    GREY52: string;
    GRAY51: string;
    GREY51: string;
    FRACTAL: string;
    GRAY50: string;
    GREY50: string;
    GRAY: string;
    GREY: string;
    GRAY49: string;
    GREY49: string;
    GRAY48: string;
    GREY48: string;
    GRAY47: string;
    GREY47: string;
    GRAY46: string;
    GREY46: string;
    GRAY45: string;
    GREY45: string;
    GRAY44: string;
    GREY44: string;
    GRAY43: string;
    GREY43: string;
    GRAY42: string;
    GREY42: string;
    DIMGRAY: string;
    DIMGREY: string;
    GRAY41: string;
    GREY41: string;
    GRAY40: string;
    GREY40: string;
    GRAY39: string;
    GREY39: string;
    GRAY38: string;
    GREY38: string;
    GRAY37: string;
    GREY37: string;
    GRAY36: string;
    GREY36: string;
    GRAY35: string;
    GREY35: string;
    GRAY34: string;
    GREY34: string;
    GRAY33: string;
    GREY33: string;
    GRAY32: string;
    GREY32: string;
    GRAY31: string;
    GREY31: string;
    GRAY30: string;
    GREY30: string;
    GRAY29: string;
    GREY29: string;
    GRAY28: string;
    GREY28: string;
    GRAY27: string;
    GREY27: string;
    GRAY26: string;
    GREY26: string;
    GRAY25: string;
    GREY25: string;
    GRAY24: string;
    GREY24: string;
    GRAY23: string;
    GREY23: string;
    GRAY22: string;
    GREY22: string;
    GRAY21: string;
    GREY21: string;
    GRAY20: string;
    GREY20: string;
    GRAY19: string;
    GREY19: string;
    GRAY18: string;
    GREY18: string;
    GRAY17: string;
    GREY17: string;
    GRAY16: string;
    GREY16: string;
    GRAY15: string;
    GREY15: string;
    GRAY14: string;
    GREY14: string;
    GRAY13: string;
    GREY13: string;
    GRAY12: string;
    GREY12: string;
    GRAY11: string;
    GREY11: string;
    GRAY10: string;
    GREY10: string;
    GRAY9: string;
    GREY9: string;
    GRAY8: string;
    GREY8: string;
    GRAY7: string;
    GREY7: string;
    GRAY6: string;
    GREY6: string;
    GRAY5: string;
    GREY5: string;
    GRAY4: string;
    GREY4: string;
    GRAY3: string;
    GREY3: string;
    GRAY2: string;
    GREY2: string;
    GRAY1: string;
    GREY1: string;
    BLACK: string;
    GRAY0: string;
    GREY0: string;
    OPAQUE: string;
    NONE: string;
    TRANSPARENT: string;
};
export { Color };
export declare type SystemColors = keyof typeof Color | string;
