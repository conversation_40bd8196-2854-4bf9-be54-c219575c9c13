{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/shared.ts"], "sourcesContent": ["import { useReducer } from 'react'\n\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { VersionInfo } from '../../../server/dev/parse-version-info'\nimport type { SupportedErrorEvent } from './ui/container/runtime-error/render-error'\nimport type { ComponentStackFrame } from './utils/parse-component-stack'\nimport type { DebugInfo } from './types'\nimport type { DevIndicatorServerState } from '../../../server/dev/dev-indicator-server-state'\nimport type { HMR_ACTION_TYPES } from '../../../server/dev/hot-reloader-types'\n\ntype FastRefreshState =\n  /** No refresh in progress. */\n  | { type: 'idle' }\n  /** The refresh process has been triggered, but the new code has not been executed yet. */\n  | { type: 'pending'; errors: SupportedErrorEvent[] }\n\nexport interface OverlayState {\n  nextId: number\n  buildError: string | null\n  errors: SupportedErrorEvent[]\n  refreshState: FastRefreshState\n  versionInfo: VersionInfo\n  notFound: boolean\n  staticIndicator: boolean\n  showIndicator: boolean\n  disableDevIndicator: boolean\n  debugInfo: DebugInfo\n  routerType: 'pages' | 'app'\n}\n\nexport const ACTION_STATIC_INDICATOR = 'static-indicator'\nexport const ACTION_BUILD_OK = 'build-ok'\nexport const ACTION_BUILD_ERROR = 'build-error'\nexport const ACTION_BEFORE_REFRESH = 'before-fast-refresh'\nexport const ACTION_REFRESH = 'fast-refresh'\nexport const ACTION_VERSION_INFO = 'version-info'\nexport const ACTION_UNHANDLED_ERROR = 'unhandled-error'\nexport const ACTION_UNHANDLED_REJECTION = 'unhandled-rejection'\nexport const ACTION_DEBUG_INFO = 'debug-info'\nexport const ACTION_DEV_INDICATOR = 'dev-indicator'\n\nexport const STORAGE_KEY_THEME = '__nextjs-dev-tools-theme'\nexport const STORAGE_KEY_POSITION = '__nextjs-dev-tools-position'\nexport const STORAGE_KEY_SCALE = '__nextjs-dev-tools-scale'\n\ninterface StaticIndicatorAction {\n  type: typeof ACTION_STATIC_INDICATOR\n  staticIndicator: boolean\n}\n\ninterface BuildOkAction {\n  type: typeof ACTION_BUILD_OK\n}\ninterface BuildErrorAction {\n  type: typeof ACTION_BUILD_ERROR\n  message: string\n}\ninterface BeforeFastRefreshAction {\n  type: typeof ACTION_BEFORE_REFRESH\n}\ninterface FastRefreshAction {\n  type: typeof ACTION_REFRESH\n}\n\nexport interface UnhandledErrorAction {\n  type: typeof ACTION_UNHANDLED_ERROR\n  reason: Error\n  frames: StackFrame[]\n  componentStackFrames?: ComponentStackFrame[]\n}\nexport interface UnhandledRejectionAction {\n  type: typeof ACTION_UNHANDLED_REJECTION\n  reason: Error\n  frames: StackFrame[]\n}\n\nexport interface DebugInfoAction {\n  type: typeof ACTION_DEBUG_INFO\n  debugInfo: any\n}\n\ninterface VersionInfoAction {\n  type: typeof ACTION_VERSION_INFO\n  versionInfo: VersionInfo\n}\n\ninterface DevIndicatorAction {\n  type: typeof ACTION_DEV_INDICATOR\n  devIndicator: DevIndicatorServerState\n}\n\nexport type BusEvent =\n  | BuildOkAction\n  | BuildErrorAction\n  | BeforeFastRefreshAction\n  | FastRefreshAction\n  | UnhandledErrorAction\n  | UnhandledRejectionAction\n  | VersionInfoAction\n  | StaticIndicatorAction\n  | DebugInfoAction\n  | DevIndicatorAction\n\nfunction pushErrorFilterDuplicates(\n  errors: SupportedErrorEvent[],\n  err: SupportedErrorEvent\n): SupportedErrorEvent[] {\n  return [\n    ...errors.filter((e) => {\n      // Filter out duplicate errors\n      return e.event.reason.stack !== err.event.reason.stack\n    }),\n    err,\n  ]\n}\n\nconst shouldDisableDevIndicator =\n  process.env.__NEXT_DEV_INDICATOR?.toString() === 'false'\n\nexport const INITIAL_OVERLAY_STATE: Omit<OverlayState, 'routerType'> = {\n  nextId: 1,\n  buildError: null,\n  errors: [],\n  notFound: false,\n  staticIndicator: false,\n  /* \n    This is set to `true` when we can reliably know\n    whether the indicator is in disabled state or not.  \n    Otherwise the surface would flicker because the disabled flag loads from the config.\n  */\n  showIndicator: false,\n  disableDevIndicator: false,\n  refreshState: { type: 'idle' },\n  versionInfo: { installed: '0.0.0', staleness: 'unknown' },\n  debugInfo: { devtoolsFrontendUrl: undefined },\n}\n\nfunction getInitialState(\n  routerType: 'pages' | 'app'\n): OverlayState & { routerType: 'pages' | 'app' } {\n  return {\n    ...INITIAL_OVERLAY_STATE,\n    routerType,\n  }\n}\n\nexport function useErrorOverlayReducer(routerType: 'pages' | 'app') {\n  return useReducer((state: OverlayState, action: BusEvent): OverlayState => {\n    switch (action.type) {\n      case ACTION_DEBUG_INFO: {\n        return { ...state, debugInfo: action.debugInfo }\n      }\n      case ACTION_STATIC_INDICATOR: {\n        return { ...state, staticIndicator: action.staticIndicator }\n      }\n      case ACTION_BUILD_OK: {\n        return { ...state, buildError: null }\n      }\n      case ACTION_BUILD_ERROR: {\n        return { ...state, buildError: action.message }\n      }\n      case ACTION_BEFORE_REFRESH: {\n        return { ...state, refreshState: { type: 'pending', errors: [] } }\n      }\n      case ACTION_REFRESH: {\n        return {\n          ...state,\n          buildError: null,\n          errors:\n            // Errors can come in during updates. In this case, UNHANDLED_ERROR\n            // and UNHANDLED_REJECTION events might be dispatched between the\n            // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n            // around until the next refresh. Otherwise we run into a race\n            // condition where those errors would be cleared on refresh completion\n            // before they can be displayed.\n            state.refreshState.type === 'pending'\n              ? state.refreshState.errors\n              : [],\n          refreshState: { type: 'idle' },\n        }\n      }\n      case ACTION_UNHANDLED_ERROR:\n      case ACTION_UNHANDLED_REJECTION: {\n        switch (state.refreshState.type) {\n          case 'idle': {\n            return {\n              ...state,\n              nextId: state.nextId + 1,\n              errors: pushErrorFilterDuplicates(state.errors, {\n                id: state.nextId,\n                event: action,\n              }),\n            }\n          }\n          case 'pending': {\n            return {\n              ...state,\n              nextId: state.nextId + 1,\n              refreshState: {\n                ...state.refreshState,\n                errors: pushErrorFilterDuplicates(state.refreshState.errors, {\n                  id: state.nextId,\n                  event: action,\n                }),\n              },\n            }\n          }\n          default:\n            return state\n        }\n      }\n      case ACTION_VERSION_INFO: {\n        return { ...state, versionInfo: action.versionInfo }\n      }\n      case ACTION_DEV_INDICATOR: {\n        return {\n          ...state,\n          showIndicator: true,\n          disableDevIndicator:\n            shouldDisableDevIndicator || !!action.devIndicator.disabledUntil,\n        }\n      }\n      default: {\n        return state\n      }\n    }\n  }, getInitialState(routerType))\n}\n\nexport const REACT_REFRESH_FULL_RELOAD =\n  '[Fast Refresh] performing full reload\\n\\n' +\n  \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n  'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n  'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n  'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n  'Fast Refresh requires at least one parent function component in your React tree.'\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n\nexport function reportInvalidHmrMessage(\n  message: HMR_ACTION_TYPES | MessageEvent<unknown>,\n  err: unknown\n) {\n  console.warn(\n    '[HMR] Invalid message: ' +\n      JSON.stringify(message) +\n      '\\n' +\n      ((err instanceof Error && err?.stack) || '')\n  )\n}\n"], "names": ["process", "useReducer", "ACTION_STATIC_INDICATOR", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "STORAGE_KEY_THEME", "STORAGE_KEY_POSITION", "STORAGE_KEY_SCALE", "pushErrorFilterDuplicates", "errors", "err", "filter", "e", "event", "reason", "stack", "shouldDisableDevIndicator", "env", "__NEXT_DEV_INDICATOR", "toString", "INITIAL_OVERLAY_STATE", "nextId", "buildError", "notFound", "staticIndicator", "showIndicator", "disableDevIndicator", "refreshState", "type", "versionInfo", "installed", "staleness", "debugInfo", "devtoolsFrontendUrl", "undefined", "getInitialState", "routerType", "useErrorOverlayReducer", "state", "action", "message", "id", "devIndicator", "disabledUntil", "REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "reportInvalidHmrMessage", "console", "warn", "JSON", "stringify", "Error"], "mappings": "IAqHEA;AArHF,SAASC,UAAU,QAAQ,QAAO;AA8BlC,OAAO,MAAMC,0BAA0B,mBAAkB;AACzD,OAAO,MAAMC,kBAAkB,WAAU;AACzC,OAAO,MAAMC,qBAAqB,cAAa;AAC/C,OAAO,MAAMC,wBAAwB,sBAAqB;AAC1D,OAAO,MAAMC,iBAAiB,eAAc;AAC5C,OAAO,MAAMC,sBAAsB,eAAc;AACjD,OAAO,MAAMC,yBAAyB,kBAAiB;AACvD,OAAO,MAAMC,6BAA6B,sBAAqB;AAC/D,OAAO,MAAMC,oBAAoB,aAAY;AAC7C,OAAO,MAAMC,uBAAuB,gBAAe;AAEnD,OAAO,MAAMC,oBAAoB,2BAA0B;AAC3D,OAAO,MAAMC,uBAAuB,8BAA6B;AACjE,OAAO,MAAMC,oBAAoB,2BAA0B;AA4D3D,SAASC,0BACPC,MAA6B,EAC7BC,GAAwB;IAExB,OAAO;WACFD,OAAOE,MAAM,CAAC,CAACC;YAChB,8BAA8B;YAC9B,OAAOA,EAAEC,KAAK,CAACC,MAAM,CAACC,KAAK,KAAKL,IAAIG,KAAK,CAACC,MAAM,CAACC,KAAK;QACxD;QACAL;KACD;AACH;AAEA,MAAMM,4BACJvB,EAAAA,oCAAAA,QAAQwB,GAAG,CAACC,oBAAoB,qBAAhCzB,kCAAkC0B,QAAQ,QAAO;AAEnD,OAAO,MAAMC,wBAA0D;IACrEC,QAAQ;IACRC,YAAY;IACZb,QAAQ,EAAE;IACVc,UAAU;IACVC,iBAAiB;IACjB;;;;EAIA,GACAC,eAAe;IACfC,qBAAqB;IACrBC,cAAc;QAAEC,MAAM;IAAO;IAC7BC,aAAa;QAAEC,WAAW;QAASC,WAAW;IAAU;IACxDC,WAAW;QAAEC,qBAAqBC;IAAU;AAC9C,EAAC;AAED,SAASC,gBACPC,UAA2B;IAE3B,OAAO;QACL,GAAGhB,qBAAqB;QACxBgB;IACF;AACF;AAEA,OAAO,SAASC,uBAAuBD,UAA2B;IAChE,OAAO1C,WAAW,CAAC4C,OAAqBC;QACtC,OAAQA,OAAOX,IAAI;YACjB,KAAKzB;gBAAmB;oBACtB,OAAO;wBAAE,GAAGmC,KAAK;wBAAEN,WAAWO,OAAOP,SAAS;oBAAC;gBACjD;YACA,KAAKrC;gBAAyB;oBAC5B,OAAO;wBAAE,GAAG2C,KAAK;wBAAEd,iBAAiBe,OAAOf,eAAe;oBAAC;gBAC7D;YACA,KAAK5B;gBAAiB;oBACpB,OAAO;wBAAE,GAAG0C,KAAK;wBAAEhB,YAAY;oBAAK;gBACtC;YACA,KAAKzB;gBAAoB;oBACvB,OAAO;wBAAE,GAAGyC,KAAK;wBAAEhB,YAAYiB,OAAOC,OAAO;oBAAC;gBAChD;YACA,KAAK1C;gBAAuB;oBAC1B,OAAO;wBAAE,GAAGwC,KAAK;wBAAEX,cAAc;4BAAEC,MAAM;4BAAWnB,QAAQ,EAAE;wBAAC;oBAAE;gBACnE;YACA,KAAKV;gBAAgB;oBACnB,OAAO;wBACL,GAAGuC,KAAK;wBACRhB,YAAY;wBACZb,QACE,mEAAmE;wBACnE,iEAAiE;wBACjE,qEAAqE;wBACrE,8DAA8D;wBAC9D,sEAAsE;wBACtE,gCAAgC;wBAChC6B,MAAMX,YAAY,CAACC,IAAI,KAAK,YACxBU,MAAMX,YAAY,CAAClB,MAAM,GACzB,EAAE;wBACRkB,cAAc;4BAAEC,MAAM;wBAAO;oBAC/B;gBACF;YACA,KAAK3B;YACL,KAAKC;gBAA4B;oBAC/B,OAAQoC,MAAMX,YAAY,CAACC,IAAI;wBAC7B,KAAK;4BAAQ;gCACX,OAAO;oCACL,GAAGU,KAAK;oCACRjB,QAAQiB,MAAMjB,MAAM,GAAG;oCACvBZ,QAAQD,0BAA0B8B,MAAM7B,MAAM,EAAE;wCAC9CgC,IAAIH,MAAMjB,MAAM;wCAChBR,OAAO0B;oCACT;gCACF;4BACF;wBACA,KAAK;4BAAW;gCACd,OAAO;oCACL,GAAGD,KAAK;oCACRjB,QAAQiB,MAAMjB,MAAM,GAAG;oCACvBM,cAAc;wCACZ,GAAGW,MAAMX,YAAY;wCACrBlB,QAAQD,0BAA0B8B,MAAMX,YAAY,CAAClB,MAAM,EAAE;4CAC3DgC,IAAIH,MAAMjB,MAAM;4CAChBR,OAAO0B;wCACT;oCACF;gCACF;4BACF;wBACA;4BACE,OAAOD;oBACX;gBACF;YACA,KAAKtC;gBAAqB;oBACxB,OAAO;wBAAE,GAAGsC,KAAK;wBAAET,aAAaU,OAAOV,WAAW;oBAAC;gBACrD;YACA,KAAKzB;gBAAsB;oBACzB,OAAO;wBACL,GAAGkC,KAAK;wBACRb,eAAe;wBACfC,qBACEV,6BAA6B,CAAC,CAACuB,OAAOG,YAAY,CAACC,aAAa;oBACpE;gBACF;YACA;gBAAS;oBACP,OAAOL;gBACT;QACF;IACF,GAAGH,gBAAgBC;AACrB;AAEA,OAAO,MAAMQ,4BACX,8CACA,mIACA,qIACA,+GACA,8HACA,mFAAkF;AAEpF,OAAO,MAAMC,uCACX,4FAA2F;AAE7F,OAAO,SAASC,wBACdN,OAAiD,EACjD9B,GAAY;IAEZqC,QAAQC,IAAI,CACV,4BACEC,KAAKC,SAAS,CAACV,WACf,OACC,CAAA,AAAC9B,eAAeyC,UAASzC,uBAAAA,IAAKK,KAAK,KAAK,EAAC;AAEhD"}