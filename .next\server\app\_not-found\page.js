(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1035:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(687),n=t(3210),i=t(9384);let o=(0,n.forwardRef)(({className:e,variant:r="primary",size:t="md",isLoading:n,children:o,disabled:a,...d},l)=>(0,s.jsxs)("button",{className:function(...e){return(0,i.$)(e)}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-600 text-white hover:bg-gray-700",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100"}[r],{sm:"h-8 px-3 text-sm",md:"h-10 px-4",lg:"h-12 px-6 text-lg"}[t],e),ref:l,disabled:a||n,...d,children:[n&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]}));o.displayName="Button"},1135:()=>{},2942:(e,r,t)=>{"use strict";t.d(r,{ReduxProvider:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\providers\\ReduxProvider.tsx","ReduxProvider")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5413:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},5736:(e,r,t)=>{"use strict";t.d(r,{Header:()=>d});var s=t(687),n=t(5814),i=t.n(n),o=t(1035),a=t(7791);function d(){return(0,s.jsx)("header",{className:"border-b border-gray-200 bg-white",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(i(),{href:a.bw.HOME,className:"text-xl font-bold text-gray-900",children:a.C3})}),(0,s.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,s.jsx)(i(),{href:a.bw.HOME,className:"text-gray-700 hover:text-gray-900",children:"Home"}),(0,s.jsx)(i(),{href:a.bw.PRODUCTS,className:"text-gray-700 hover:text-gray-900",children:"Products"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(i(),{href:a.bw.LOGIN,children:(0,s.jsx)(o.$,{variant:"ghost",children:"Login"})}),(0,s.jsx)(i(),{href:a.bw.REGISTER,children:(0,s.jsx)(o.$,{children:"Sign Up"})})]})]})})})}},5937:(e,r,t)=>{Promise.resolve().then(t.bind(t,8926)),Promise.resolve().then(t.bind(t,2942))},6209:(e,r,t)=>{Promise.resolve().then(t.bind(t,5736)),Promise.resolve().then(t.bind(t,8864))},7269:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},7791:(e,r,t)=>{"use strict";t.d(r,{C3:()=>s,bw:()=>n});let s="Caparan Tindahan",n={HOME:"/",LOGIN:"/login",REGISTER:"/register",DASHBOARD:"/dashboard",PROFILE:"/profile",PRODUCTS:"/products"}},7936:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>x});var s=t(7413),n=t(2376),i=t.n(n),o=t(8726),a=t.n(o);t(1135);var d=t(2942),l=t(8926);function c(){return(0,s.jsx)("footer",{className:"border-t border-gray-200 bg-white",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["\xa9 ",new Date().getFullYear()," ","Caparan Tindahan",". All rights reserved."]}),(0,s.jsxs)("div",{className:"flex space-x-6",children:[(0,s.jsx)("a",{href:"#",className:"text-sm text-gray-600 hover:text-gray-900",children:"Privacy Policy"}),(0,s.jsx)("a",{href:"#",className:"text-sm text-gray-600 hover:text-gray-900",children:"Terms of Service"}),(0,s.jsx)("a",{href:"#",className:"text-sm text-gray-600 hover:text-gray-900",children:"Contact"})]})]})})})}function h({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,s.jsx)(l.Header,{}),(0,s.jsx)("main",{className:"flex-1",children:e}),(0,s.jsx)(c,{})]})}let x={title:"Caparan Tindahan",description:"Professional e-commerce platform built with Next.js 15"};function m({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,s.jsx)(d.ReduxProvider,{children:(0,s.jsx)(h,{children:e})})})})}},8316:(e,r,t)=>{"use strict";t.d(r,{M_:()=>c,GV:()=>h});var s=t(9317),n=t(4864);let i=(0,s.Z0)({name:"auth",initialState:{user:null,isLoading:!1,isAuthenticated:!1},reducers:{setUser:(e,r)=>{e.user=r.payload,e.isAuthenticated=!0,e.isLoading=!1},clearUser:e=>{e.user=null,e.isAuthenticated=!1,e.isLoading=!1},setLoading:(e,r)=>{e.isLoading=r.payload}}}),{setUser:o,clearUser:a,setLoading:d}=i.actions,l=i.reducer,c=(0,s.U1)({reducer:{auth:l},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"]}})}),h=n.d4},8837:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=t(5239),n=t(8088),i=t(8170),o=t.n(i),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,7936)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],h={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8864:(e,r,t)=>{"use strict";t.d(r,{ReduxProvider:()=>o});var s=t(687),n=t(4864),i=t(8316);function o({children:e}){return(0,s.jsx)(n.Kq,{store:i.M_,children:e})}},8926:(e,r,t)=>{"use strict";t.d(r,{Header:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\caparan-tindahan\\src\\components\\layout\\Header.tsx","Header")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,374],()=>t(8837));module.exports=s})();