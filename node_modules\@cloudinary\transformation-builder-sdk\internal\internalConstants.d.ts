/**
 * This file is for internal constants only.
 * It is not intended for public use and is not part of the public API
 */
export declare const CONDITIONAL_OPERATORS: {
    "=": string;
    "!=": string;
    "<": string;
    ">": string;
    "<=": string;
    ">=": string;
    "&&": string;
    "||": string;
    "*": string;
    "/": string;
    "+": string;
    "-": string;
    "^": string;
};
export declare const RESERVED_NAMES: {
    aspect_ratio: string;
    aspectRatio: string;
    current_page: string;
    currentPage: string;
    duration: string;
    face_count: string;
    faceCount: string;
    height: string;
    initial_aspect_ratio: string;
    initial_height: string;
    initial_width: string;
    initialAspectRatio: string;
    initialHeight: string;
    initialWidth: string;
    initial_duration: string;
    initialDuration: string;
    page_count: string;
    page_x: string;
    page_y: string;
    pageCount: string;
    pageX: string;
    pageY: string;
    tags: string;
    width: string;
    trimmed_aspect_ratio: string;
    current_public_id: string;
    initial_density: string;
    page_names: string;
};
export declare const ACTION_TYPE_TO_CROP_MODE_MAP: Record<string, string>;
export declare const ACTION_TYPE_TO_DELIVERY_MODE_MAP: Record<string, string>;
export declare const ACTION_TYPE_TO_EFFECT_MODE_MAP: Record<string, string>;
export declare const ACTION_TYPE_TO_QUALITY_MODE_MAP: Record<string, string>;
export declare const ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP: Record<string, string>;
export declare const CHROMA_VALUE_TO_CHROMA_MODEL_ENUM: Record<number, string>;
export declare const COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP: Record<string, string>;
export declare const ACTION_TYPE_TO_BLEND_MODE_MAP: Record<string, string>;
export declare const CHROMA_MODEL_ENUM_TO_CHROMA_VALUE: Record<string, string>;
export declare const COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP: Record<string, string>;
export declare const CROP_MODE_TO_ACTION_TYPE_MAP: Record<string, string>;
export declare const DELIVERY_MODE_TO_ACTION_TYPE_MAP: Record<string, string>;
export declare const EFFECT_MODE_TO_ACTION_TYPE_MAP: Record<string, string>;
export declare const QUALITY_MODE_TO_ACTION_TYPE_MAP: Record<string, string>;
export declare const STREAMING_PROFILE_TO_ACTION_TYPE_MAP: Record<string, string>;
