import { ITransformationFromJson } from "./IHasFromJson.js";
import { ISourceModel } from "./ISourceModel.js";
import { BaseSource } from "../../qualifiers/source/BaseSource.js";
/**
 * Create Source from given model json
 * @param source
 * @param transformationFromJson
 */
export declare function createSourceFromModel(source: ISourceModel, transformationFromJson: ITransformationFromJson): BaseSource;
