import { EffectActions, Effect, pixelate, deshake, boomerang, advancedRedEye, blackwhite, negate, redEye, reverse, accelerate, fadeIn, fadeOut, loop, makeTransparent, noise, vignette, blur, grayscale, sepia, shadow, colorize, oilPaint, artisticFilter, cartoonify, outline, styleTransfer, gradientFade, vectorize, assistColorBlind, simulateColorBlind, transition, dither, removeBackground, backgroundRemoval, dropShadow, generativeRemove, generativeReplace, generativeRecolor, generativeRestore, upscale, theme } from "@cloudinary/transformation-builder-sdk/actions/effect";
export { EffectActions, Effect, pixelate, deshake, boomerang, advancedRedEye, blackwhite, negate, redEye, reverse, accelerate, fadeIn, fadeOut, loop, makeTransparent, noise, vignette, blur, grayscale, sepia, shadow, colorize, oilPaint, artisticFilter, cartoonify, outline, styleTransfer, gradientFade, vectorize, assistColorBlind, simulateColorBlind, transition, dither, removeBackground, backgroundRemoval, dropShadow, generativeRemove, generativeReplace, generativeRecolor, generativeRestore, upscale, theme };
