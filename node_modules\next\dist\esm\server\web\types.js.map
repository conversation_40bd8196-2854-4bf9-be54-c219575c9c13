{"version": 3, "sources": ["../../../src/server/web/types.ts"], "sourcesContent": ["import type { ExperimentalConfig, I18NConfig } from '../config-shared'\nimport type { NextRequest } from './spec-extension/request'\nimport type { NextFetchEvent } from './spec-extension/fetch-event'\nimport type { NextResponse } from './spec-extension/response'\nimport type { CloneableBody } from '../body-streams'\nimport type { OutgoingHttpHeaders } from 'http'\nimport type { FetchMetrics } from '../base-http'\n\nexport type { MiddlewareConfigInput as MiddlewareConfig } from '../../build/segment-config/middleware/middleware-config'\n\nexport interface RequestData {\n  headers: OutgoingHttpHeaders\n  method: string\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n    experimental?: Pick<ExperimentalConfig, 'cacheLife' | 'authInterrupts'>\n  }\n  page?: {\n    name?: string\n    params?: { [key: string]: string | string[] | undefined }\n  }\n  url: string\n  body?: ReadableStream<Uint8Array>\n  signal: AbortSignal\n  /** passed in when running in edge runtime sandbox */\n  waitUntil?: (promise: Promise<any>) => void\n}\n\nexport type NodejsRequestData = Omit<RequestData, 'body'> & {\n  body?: CloneableBody\n}\n\nexport interface FetchEventResult {\n  response: Response\n  waitUntil: Promise<any>\n  fetchMetrics?: FetchMetrics\n}\n\nexport type NextMiddlewareResult =\n  | NextResponse\n  | Response\n  | null\n  | undefined\n  | void\n\n/**\n * Middleware allows you to run code before a request is completed.\n * Then, based on the incoming request, you can modify the response\n * by rewriting, redirecting, modifying the request or response headers,\n * or responding directly.\n *\n * Read more: [Next.js Docs: Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware)\n */\nexport type NextMiddleware = (\n  request: NextRequest,\n  event: NextFetchEvent\n) => NextMiddlewareResult | Promise<NextMiddlewareResult>\n"], "names": [], "mappings": "AA+CA;;;;;;;CAOC,GACD,WAGyD"}