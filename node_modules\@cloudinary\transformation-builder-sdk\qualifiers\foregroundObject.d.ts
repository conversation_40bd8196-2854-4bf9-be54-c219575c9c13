/**
 * @memberOf Qualifiers
 * @description This namespace contains all the foreground objects used in the SDK
 * @namespace ForegroundObject
 * @example
 * import {ForegroundObject} from '@cloudinary/url-gen/qualifiers/foregroundObject'
 */
declare const FOREGROUND_OBJECT: {
    readonly AIRPLANE: "airplane";
    readonly BUS: "bus";
    readonly DINING_TABLE: "dining_table";
    readonly SHEEP: "sheep";
    readonly BICYCLE: "bicycle";
    readonly CAR: "car";
    readonly DOG: "dog";
    readonly SOFA: "sofa";
    readonly BIRD: "bird";
    readonly CAT: "cat";
    readonly HORSE: "horse";
    readonly TRAIN: "train";
    readonly BOAT: "boat";
    readonly CHAIR: "chair";
    readonly PERSON: "person";
    readonly TV: "tv";
    readonly BOTTLE: "bottle";
    readonly COW: "cow";
    readonly POTTED_PLANT: "potted_plant";
    readonly MOTORBIKE: "motorbike";
};
export declare type ForegroundObjectValue = typeof FOREGROUND_OBJECT[keyof typeof FOREGROUND_OBJECT];
/**
 * @summary qualifier
 * @description Foreground object "airplane".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const airplane: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "bus".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const bus: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "dining_table".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const diningTable: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "sheep".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const sheep: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "bicycle".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const bicycle: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "car".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const car: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "dog".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const dog: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "sofa".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const sofa: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "bird".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const bird: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "cat".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const cat: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "horse".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const horse: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "train".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const train: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "boat".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const boat: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "chair".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const chair: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "person".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const person: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "tv".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const tv: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "bottle".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const bottle: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "cow".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const cow: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "potted_plant".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const pottedPlant: () => ForegroundObjectValue;
/**
 * @summary qualifier
 * @description Foreground object "motorbike".
 * @memberOf Qualifiers.ForegroundObject
 * @return {Qualifiers.ForegroundObject}
 */
export declare const motorbike: () => ForegroundObjectValue;
export declare const ForegroundObject: {
    airplane: () => ForegroundObjectValue;
    bus: () => ForegroundObjectValue;
    diningTable: () => ForegroundObjectValue;
    sheep: () => ForegroundObjectValue;
    bicycle: () => ForegroundObjectValue;
    car: () => ForegroundObjectValue;
    dog: () => ForegroundObjectValue;
    sofa: () => ForegroundObjectValue;
    bird: () => ForegroundObjectValue;
    cat: () => ForegroundObjectValue;
    horse: () => ForegroundObjectValue;
    train: () => ForegroundObjectValue;
    boat: () => ForegroundObjectValue;
    chair: () => ForegroundObjectValue;
    person: () => ForegroundObjectValue;
    tv: () => ForegroundObjectValue;
    bottle: () => ForegroundObjectValue;
    cow: () => ForegroundObjectValue;
    pottedPlant: () => ForegroundObjectValue;
    motorbike: () => ForegroundObjectValue;
    AIRPLANE: "airplane";
    BUS: "bus";
    DINING_TABLE: "dining_table";
    SHEEP: "sheep";
    BICYCLE: "bicycle";
    CAR: "car";
    DOG: "dog";
    SOFA: "sofa";
    BIRD: "bird";
    CAT: "cat";
    HORSE: "horse";
    TRAIN: "train";
    BOAT: "boat";
    CHAIR: "chair";
    PERSON: "person";
    TV: "tv";
    BOTTLE: "bottle";
    COW: "cow";
    POTTED_PLANT: "potted_plant";
    MOTORBIKE: "motorbike";
};
export {};
