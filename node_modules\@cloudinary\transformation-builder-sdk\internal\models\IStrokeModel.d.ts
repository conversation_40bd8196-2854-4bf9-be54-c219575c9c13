import { IQualifierModel } from "./IQualifierModel.js";
import { IColorModel } from "./IColorModel.js";
export interface ISolidStrokeModel extends IQualifierModel {
    width: number | string;
    color: IColorModel;
}
export declare type IStrokeModel = boolean | ISolidStrokeModel;
/**
 * Validate that obj is an ISolidStrokeModel
 * @param obj
 */
export declare function isISolidStrokeModel(obj: unknown): obj is ISolidStrokeModel;
