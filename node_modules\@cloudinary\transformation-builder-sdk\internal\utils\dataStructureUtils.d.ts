import { FlagQualifier } from "../../qualifiers/flag/FlagQualifier.js";
/**
 * Sort a map by key
 * @private
 * @param map <string, any>
 * @Return array of map's values sorted by key
 */
declare function mapToSortedArray<T>(map: Map<string, T | FlagQualifier>, flags: FlagQualifier[]): (T | FlagQualifier)[];
/**
 * Checks if `value` is a string.
 * @private
 * @param {*} value The value to check.
 * @return {boolean} `true` if `value` is a string, else `false`.
 */
declare function isString(value: any): value is string;
export { isString, mapToSortedArray };
