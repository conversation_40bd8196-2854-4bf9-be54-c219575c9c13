/**
 * @description Contains functions to select the type of improvement to perform when using Adjust.improve().
 * @namespace Outline
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Effect|Effect Action} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.Outline
 */
declare function fill(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Outline
 */
declare function inner(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Outline
 */
declare function innerFill(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Outline
 */
declare function outer(): string;
declare const OutlineMode: {
    outer: typeof outer;
    inner: typeof inner;
    innerFill: typeof innerFill;
    fill: typeof fill;
};
export { OutlineMode, outer, inner, innerFill, fill };
