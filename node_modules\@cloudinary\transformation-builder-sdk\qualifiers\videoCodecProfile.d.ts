/**
 * @description Contains functions to select the video codec profile.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_manipulation_and_delivery#video_codec_settings|Video codec settings}
 * @memberOf Qualifiers
 * @namespace VideoCodecProfile
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecProfile
 * @return {string}
 */
declare function high(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecProfile
 * @return {string}
 */
declare function main(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecProfile
 * @return {string}
 */
declare function baseline(): string;
declare const VideoCodecProfile: {
    baseline: typeof baseline;
    main: typeof main;
    high: typeof high;
};
export { VideoCodecProfile, baseline, main, high };
